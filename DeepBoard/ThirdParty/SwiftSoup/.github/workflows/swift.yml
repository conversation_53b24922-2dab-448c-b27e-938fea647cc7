name: Swift



on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:
    name: Swift ${{ matrix.swift }} on ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-22.04, macos-latest]
        swift: ["5", "5.9"]
        
    runs-on: ${{ matrix.os }}

    steps:
    - uses: swift-actions/setup-swift@v2.3.0
      with:
        swift-version: ${{ matrix.swift }}
    - uses: actions/checkout@v4
    - name: Build
      run: swift build -v
    - name: Run tests
      run: swift test -v
