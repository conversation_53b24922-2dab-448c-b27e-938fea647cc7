<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="MUx-1p-XmN">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--SwiftSoup-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController title="SwiftSoup" id="BYZ-38-t0r" customClass="ViewController" customModule="Example" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="603"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="58j-Vf-f4X">
                                <rect key="frame" x="16" y="49" width="343" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="mTJ-Hq-uyq"/>
                                </constraints>
                                <color key="textColor" red="0.031372549020000001" green="0.66666666669999997" blue="0.27058823529999998" alpha="1" colorSpace="calibratedRGB"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocorrectionType="no" returnKeyType="done" textContentType="url"/>
                                <connections>
                                    <outlet property="delegate" destination="BYZ-38-t0r" id="Fe6-6k-ZcP"/>
                                </connections>
                            </textField>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="URL" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="h6V-hR-wl7">
                                <rect key="frame" x="16" y="20" width="343" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="24" id="bRC-DM-8SJ"/>
                                </constraints>
                                <fontDescription key="fontDescription" style="UICTFontTextStyleTitle3"/>
                                <color key="textColor" red="0.2156862745" green="0.26274509800000001" blue="0.2156862745" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="UaL-bt-Vf1">
                                <rect key="frame" x="16" y="124" width="343" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="DjS-Py-R3g"/>
                                </constraints>
                                <color key="textColor" red="0.031372549020000001" green="0.66666666669999997" blue="0.27058823529999998" alpha="1" colorSpace="calibratedRGB"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocorrectionType="no" returnKeyType="done"/>
                                <connections>
                                    <outlet property="delegate" destination="BYZ-38-t0r" id="FHN-HO-v0N"/>
                                </connections>
                            </textField>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="CSS Selector" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TKO-Iu-RQr">
                                <rect key="frame" x="16" y="95" width="248" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="24" id="nGo-Vq-uac"/>
                                </constraints>
                                <fontDescription key="fontDescription" style="UICTFontTextStyleTitle3"/>
                                <color key="textColor" red="0.2156862745" green="0.26274509800000001" blue="0.2156862745" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="K3B-Ml-qgz">
                                <rect key="frame" x="272" y="89" width="87" height="30"/>
                                <color key="backgroundColor" red="0.031372549020000001" green="0.66666666669999997" blue="0.27058823529999998" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="87" id="3N4-yK-XaC"/>
                                    <constraint firstAttribute="height" constant="30" id="Rzz-Lg-yfA"/>
                                </constraints>
                                <state key="normal" title="Examples">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="chooseQuery:" destination="BYZ-38-t0r" eventType="touchUpInside" id="NAW-kg-wad"/>
                                </connections>
                            </button>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="bYN-Yv-noI">
                                <rect key="frame" x="0.0" y="162" width="375" height="441"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <connections>
                                    <outlet property="dataSource" destination="BYZ-38-t0r" id="hZQ-9T-uRE"/>
                                    <outlet property="delegate" destination="BYZ-38-t0r" id="EGy-Ic-gvN"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" red="0.88627450980392153" green="0.93725490196078431" blue="0.85882352941176465" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="bYN-Yv-noI" firstAttribute="bottom" secondItem="6Tk-OE-BBY" secondAttribute="bottom" id="2a6-Qo-GkU"/>
                            <constraint firstItem="h6V-hR-wl7" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="16" id="2op-Vd-9cA"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="K3B-Ml-qgz" secondAttribute="trailing" constant="16" id="5lt-pn-NCz"/>
                            <constraint firstItem="58j-Vf-f4X" firstAttribute="top" secondItem="h6V-hR-wl7" secondAttribute="bottom" constant="5" id="6xU-R1-JwJ"/>
                            <constraint firstItem="58j-Vf-f4X" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="16" id="7rE-n8-ohi"/>
                            <constraint firstItem="TKO-Iu-RQr" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="16" id="9NX-Z6-wcb"/>
                            <constraint firstItem="K3B-Ml-qgz" firstAttribute="top" secondItem="58j-Vf-f4X" secondAttribute="bottom" constant="10" id="H93-gy-eRo"/>
                            <constraint firstItem="TKO-Iu-RQr" firstAttribute="top" secondItem="58j-Vf-f4X" secondAttribute="bottom" constant="16" id="HvJ-xg-a5R"/>
                            <constraint firstItem="UaL-bt-Vf1" firstAttribute="top" secondItem="TKO-Iu-RQr" secondAttribute="bottom" constant="5" id="Lf5-mK-ffe"/>
                            <constraint firstItem="bYN-Yv-noI" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="Ma2-r4-bnP"/>
                            <constraint firstItem="UaL-bt-Vf1" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="16" id="PeU-NY-TgI"/>
                            <constraint firstItem="bYN-Yv-noI" firstAttribute="top" secondItem="UaL-bt-Vf1" secondAttribute="bottom" constant="8" id="RJk-tx-Sqf"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="UaL-bt-Vf1" secondAttribute="trailing" constant="16" id="W9f-Wb-pVW"/>
                            <constraint firstItem="bYN-Yv-noI" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="aJ0-Ye-UMt"/>
                            <constraint firstItem="h6V-hR-wl7" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="eTF-Cc-46O"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="h6V-hR-wl7" secondAttribute="trailing" constant="16" id="h2E-Cn-lpB"/>
                            <constraint firstItem="K3B-Ml-qgz" firstAttribute="leading" secondItem="TKO-Iu-RQr" secondAttribute="trailing" constant="8" id="j9W-hd-MKo"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="58j-Vf-f4X" secondAttribute="trailing" constant="16" id="yze-Qs-zar"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                    <navigationItem key="navigationItem" id="qcl-6r-cqb"/>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <connections>
                        <outlet property="cssTextField" destination="UaL-bt-Vf1" id="aD4-1C-Afu"/>
                        <outlet property="tableView" destination="bYN-Yv-noI" id="fF0-dc-TKp"/>
                        <outlet property="urlTextField" destination="58j-Vf-f4X" id="X1e-cO-qKd"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="264.80000000000001" y="89.505247376311857"/>
        </scene>
        <!--Examples-->
        <scene sceneID="jfY-E6-1VE">
            <objects>
                <viewController storyboardIdentifier="QueryViewController" title="Examples" useStoryboardIdentifierAsRestorationIdentifier="YES" id="DAh-1g-go8" customClass="QueryViewController" customModule="Example" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="vCS-de-NKf">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="Akh-V5-8JV">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="QueryViewControllerCell" rowHeight="129" id="HXH-rN-HN6" customClass="QueryViewControllerCell" customModule="Example" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="28" width="375" height="129"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="HXH-rN-HN6" id="QHs-R3-ygw">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="128.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=".class" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="8" translatesAutoresizingMaskIntoConstraints="NO" id="VYl-uB-12K">
                                                    <rect key="frame" x="103" y="11" width="257" height="21"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="21" id="x1z-4S-mjd"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="italicSystem" pointSize="16"/>
                                                    <color key="textColor" red="0.031372549019607843" green="0.66666666666666663" blue="0.27058823529411763" alpha="1" colorSpace="calibratedRGB"/>
                                                    <color key="highlightedColor" white="0.33333333333333331" alpha="1" colorSpace="calibratedWhite"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=".intro" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="KoO-v6-8Lb">
                                                    <rect key="frame" x="103" y="40" width="257" height="21"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="21" id="kQ9-o0-oMK"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                                    <color key="textColor" red="0.031372549019607843" green="0.66666666666666663" blue="0.27058823529411763" alpha="1" colorSpace="calibratedRGB"/>
                                                    <color key="highlightedColor" white="0.33333333333333331" alpha="1" colorSpace="calibratedWhite"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Selector" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bFV-ta-5hb">
                                                    <rect key="frame" x="15" y="11" width="80" height="21"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="21" id="oIO-VZ-rzE"/>
                                                        <constraint firstAttribute="width" constant="80" id="wOj-7r-0wp"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                    <color key="textColor" red="0.21568627450980393" green="0.2627450980392157" blue="0.21568627450980393" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Example" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="91v-w8-x7w">
                                                    <rect key="frame" x="15" y="40" width="80" height="21"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="80" id="6yc-AE-ggD"/>
                                                        <constraint firstAttribute="height" constant="21" id="81f-xn-7CO"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                    <color key="textColor" red="0.21568627450980393" green="0.2627450980392157" blue="0.21568627450980393" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Description" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4ze-XW-dHI">
                                                    <rect key="frame" x="15" y="69" width="80" height="21"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="21" id="CMl-Y3-Hur"/>
                                                        <constraint firstAttribute="width" constant="80" id="eSm-ri-0K2"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                    <color key="textColor" red="0.21568627450980393" green="0.2627450980392157" blue="0.21568627450980393" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Selects all elements with a title attribute containing the word &quot;flower&quot;" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tiF-8v-PzX">
                                                    <rect key="frame" x="103" y="69" width="257" height="49"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="28" id="qXV-8g-8k6"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" style="UICTFontTextStyleSubhead"/>
                                                    <color key="textColor" red="0.031372549019607843" green="0.66666666666666663" blue="0.27058823529411763" alpha="1" colorSpace="calibratedRGB"/>
                                                    <color key="highlightedColor" white="0.33333333333333331" alpha="1" colorSpace="calibratedWhite"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="91v-w8-x7w" firstAttribute="top" secondItem="bFV-ta-5hb" secondAttribute="bottom" constant="8" id="0R6-BM-oDe"/>
                                                <constraint firstItem="KoO-v6-8Lb" firstAttribute="top" secondItem="VYl-uB-12K" secondAttribute="bottom" constant="8" id="3Hd-N2-pvo"/>
                                                <constraint firstItem="4ze-XW-dHI" firstAttribute="top" secondItem="91v-w8-x7w" secondAttribute="bottom" constant="8" id="H8R-qU-u7h"/>
                                                <constraint firstItem="VYl-uB-12K" firstAttribute="top" secondItem="QHs-R3-ygw" secondAttribute="topMargin" id="UVf-wZ-57q"/>
                                                <constraint firstItem="tiF-8v-PzX" firstAttribute="leading" secondItem="4ze-XW-dHI" secondAttribute="trailing" constant="8" id="XB9-Bk-oEA"/>
                                                <constraint firstItem="tiF-8v-PzX" firstAttribute="trailing" secondItem="QHs-R3-ygw" secondAttribute="trailingMargin" id="b3b-u2-8tg"/>
                                                <constraint firstItem="VYl-uB-12K" firstAttribute="leading" secondItem="bFV-ta-5hb" secondAttribute="trailing" constant="8" id="dmV-Qc-7Wl"/>
                                                <constraint firstAttribute="bottomMargin" secondItem="tiF-8v-PzX" secondAttribute="bottom" id="gj0-fR-Vq3"/>
                                                <constraint firstItem="KoO-v6-8Lb" firstAttribute="leading" secondItem="91v-w8-x7w" secondAttribute="trailing" constant="8" id="kaC-nA-1ci"/>
                                                <constraint firstItem="bFV-ta-5hb" firstAttribute="leading" secondItem="QHs-R3-ygw" secondAttribute="leadingMargin" id="kvn-tX-2jI"/>
                                                <constraint firstItem="KoO-v6-8Lb" firstAttribute="trailing" secondItem="QHs-R3-ygw" secondAttribute="trailingMargin" id="lEW-xl-Nq5"/>
                                                <constraint firstItem="bFV-ta-5hb" firstAttribute="top" secondItem="QHs-R3-ygw" secondAttribute="topMargin" id="oD3-SY-89D"/>
                                                <constraint firstItem="tiF-8v-PzX" firstAttribute="top" secondItem="KoO-v6-8Lb" secondAttribute="bottom" constant="8" id="pOP-ua-0wv"/>
                                                <constraint firstItem="4ze-XW-dHI" firstAttribute="leading" secondItem="QHs-R3-ygw" secondAttribute="leadingMargin" id="wDm-bi-dqY"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="VYl-uB-12K" secondAttribute="trailing" id="y4I-hc-0tj"/>
                                                <constraint firstItem="91v-w8-x7w" firstAttribute="leading" secondItem="QHs-R3-ygw" secondAttribute="leadingMargin" id="z1X-AM-faw"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="descriptionLabel" destination="tiF-8v-PzX" id="Fzi-Hl-yRV"/>
                                            <outlet property="example" destination="KoO-v6-8Lb" id="93g-lf-QHG"/>
                                            <outlet property="selector" destination="VYl-uB-12K" id="tNn-yV-qJi"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="DAh-1g-go8" id="9Ig-zC-BEV"/>
                                    <outlet property="delegate" destination="DAh-1g-go8" id="9U0-rT-SKN"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" red="0.8862745098" green="0.93725490199999995" blue="0.85882352939999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="Akh-V5-8JV" firstAttribute="leading" secondItem="gfm-8x-Gbs" secondAttribute="leading" id="Uby-W9-Hwr"/>
                            <constraint firstItem="Akh-V5-8JV" firstAttribute="trailing" secondItem="gfm-8x-Gbs" secondAttribute="trailing" id="fYO-K9-lmD"/>
                            <constraint firstItem="Akh-V5-8JV" firstAttribute="top" secondItem="vCS-de-NKf" secondAttribute="top" id="gzY-2u-fBs"/>
                            <constraint firstItem="Akh-V5-8JV" firstAttribute="bottom" secondItem="gfm-8x-Gbs" secondAttribute="bottom" id="qDC-v3-vb0"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="gfm-8x-Gbs"/>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="Akh-V5-8JV" id="IUf-Zx-rZV"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4h8-ln-Pnx" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="935.20000000000005" y="89.505247376311857"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="Ez8-Q5-bkH">
            <objects>
                <navigationController id="MUx-1p-XmN" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Fhp-80-hVg">
                        <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="tintColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="barTintColor" red="0.031372549020000001" green="0.66666666669999997" blue="0.27058823529999998" alpha="1" colorSpace="calibratedRGB"/>
                        <textAttributes key="titleTextAttributes">
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <color key="textShadowColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </textAttributes>
                        <textAttributes key="largeTitleTextAttributes">
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <color key="textShadowColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </textAttributes>
                    </navigationBar>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="2yL-fK-r3V"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="yQd-oe-5wl" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-580" y="90"/>
        </scene>
    </scenes>
</document>
