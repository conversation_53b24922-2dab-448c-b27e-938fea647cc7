// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		BDD73C662049FCB0004552B8 /* QueryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD73C652049FCB0004552B8 /* QueryViewController.swift */; };
		BDFD443B1F86762A00EA93D2 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFD443A1F86762A00EA93D2 /* AppDelegate.swift */; };
		BDFD443D1F86762A00EA93D2 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFD443C1F86762A00EA93D2 /* ViewController.swift */; };
		BDFD44401F86762A00EA93D2 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BDFD443E1F86762A00EA93D2 /* Main.storyboard */; };
		BDFD44421F86762A00EA93D2 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = BDFD44411F86762A00EA93D2 /* Assets.xcassets */; };
		BDFD44451F86762A00EA93D2 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BDFD44431F86762A00EA93D2 /* LaunchScreen.storyboard */; };
		FDC840C47D45C59DD2A01E2E /* Pods_Example.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 83005CF2B27E6268736DE283 /* Pods_Example.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		78C7D84AB09AF703DC668175 /* Pods-Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Example.release.xcconfig"; path = "Pods/Target Support Files/Pods-Example/Pods-Example.release.xcconfig"; sourceTree = "<group>"; };
		83005CF2B27E6268736DE283 /* Pods_Example.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Example.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BDD73C652049FCB0004552B8 /* QueryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QueryViewController.swift; sourceTree = "<group>"; };
		BDFD44371F86762A00EA93D2 /* Example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Example.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BDFD443A1F86762A00EA93D2 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		BDFD443C1F86762A00EA93D2 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		BDFD443F1F86762A00EA93D2 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		BDFD44411F86762A00EA93D2 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		BDFD44441F86762A00EA93D2 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		BDFD44461F86762A00EA93D2 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E0FB07C970809645864183BD /* Pods-Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Example.debug.xcconfig"; path = "Pods/Target Support Files/Pods-Example/Pods-Example.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		BDFD44341F86762A00EA93D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDC840C47D45C59DD2A01E2E /* Pods_Example.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		BDFD442E1F86762A00EA93D2 = {
			isa = PBXGroup;
			children = (
				BDFD44391F86762A00EA93D2 /* Example */,
				BDFD44381F86762A00EA93D2 /* Products */,
				CC7906FD127436CC0328A460 /* Pods */,
				F658BC9C346316CA1364330C /* Frameworks */,
			);
			indentWidth = 4;
			sourceTree = "<group>";
			tabWidth = 4;
		};
		BDFD44381F86762A00EA93D2 /* Products */ = {
			isa = PBXGroup;
			children = (
				BDFD44371F86762A00EA93D2 /* Example.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BDFD44391F86762A00EA93D2 /* Example */ = {
			isa = PBXGroup;
			children = (
				BDFD443A1F86762A00EA93D2 /* AppDelegate.swift */,
				BDFD443C1F86762A00EA93D2 /* ViewController.swift */,
				BDD73C652049FCB0004552B8 /* QueryViewController.swift */,
				BDFD443E1F86762A00EA93D2 /* Main.storyboard */,
				BDFD44411F86762A00EA93D2 /* Assets.xcassets */,
				BDFD44431F86762A00EA93D2 /* LaunchScreen.storyboard */,
				BDFD44461F86762A00EA93D2 /* Info.plist */,
			);
			path = Example;
			sourceTree = "<group>";
		};
		CC7906FD127436CC0328A460 /* Pods */ = {
			isa = PBXGroup;
			children = (
				E0FB07C970809645864183BD /* Pods-Example.debug.xcconfig */,
				78C7D84AB09AF703DC668175 /* Pods-Example.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		F658BC9C346316CA1364330C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				83005CF2B27E6268736DE283 /* Pods_Example.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		BDFD44361F86762A00EA93D2 /* Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BDFD44491F86762A00EA93D2 /* Build configuration list for PBXNativeTarget "Example" */;
			buildPhases = (
				2D22E23DE2D8DEE09CCD9E5B /* [CP] Check Pods Manifest.lock */,
				BDFD44331F86762A00EA93D2 /* Sources */,
				BDFD44341F86762A00EA93D2 /* Frameworks */,
				BDFD44351F86762A00EA93D2 /* Resources */,
				507A2DFA1AEFB55CE53D37DF /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Example;
			productName = Example;
			productReference = BDFD44371F86762A00EA93D2 /* Example.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BDFD442F1F86762A00EA93D2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0900;
				LastUpgradeCheck = 1000;
				ORGANIZATIONNAME = Nabil;
				TargetAttributes = {
					BDFD44361F86762A00EA93D2 = {
						CreatedOnToolsVersion = 9.0;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = BDFD44321F86762A00EA93D2 /* Build configuration list for PBXProject "Example" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = BDFD442E1F86762A00EA93D2;
			productRefGroup = BDFD44381F86762A00EA93D2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				BDFD44361F86762A00EA93D2 /* Example */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		BDFD44351F86762A00EA93D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BDFD44451F86762A00EA93D2 /* LaunchScreen.storyboard in Resources */,
				BDFD44421F86762A00EA93D2 /* Assets.xcassets in Resources */,
				BDFD44401F86762A00EA93D2 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2D22E23DE2D8DEE09CCD9E5B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Example-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		507A2DFA1AEFB55CE53D37DF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Example/Pods-Example-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/SwiftSoup/SwiftSoup.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftSoup.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Example/Pods-Example-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		BDFD44331F86762A00EA93D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BDFD443D1F86762A00EA93D2 /* ViewController.swift in Sources */,
				BDFD443B1F86762A00EA93D2 /* AppDelegate.swift in Sources */,
				BDD73C662049FCB0004552B8 /* QueryViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		BDFD443E1F86762A00EA93D2 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				BDFD443F1F86762A00EA93D2 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		BDFD44431F86762A00EA93D2 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				BDFD44441F86762A00EA93D2 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		BDFD44471F86762A00EA93D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		BDFD44481F86762A00EA93D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		BDFD444A1F86762A00EA93D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E0FB07C970809645864183BD /* Pods-Example.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = CR2PK929N8;
				INFOPLIST_FILE = Example/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.scinfu.Example;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BDFD444B1F86762A00EA93D2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 78C7D84AB09AF703DC668175 /* Pods-Example.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = CR2PK929N8;
				INFOPLIST_FILE = Example/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.scinfu.Example;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		BDFD44321F86762A00EA93D2 /* Build configuration list for PBXProject "Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BDFD44471F86762A00EA93D2 /* Debug */,
				BDFD44481F86762A00EA93D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BDFD44491F86762A00EA93D2 /* Build configuration list for PBXNativeTarget "Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BDFD444A1F86762A00EA93D2 /* Debug */,
				BDFD444B1F86762A00EA93D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BDFD442F1F86762A00EA93D2 /* Project object */;
}
