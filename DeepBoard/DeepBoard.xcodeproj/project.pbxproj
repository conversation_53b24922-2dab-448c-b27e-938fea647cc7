// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		8E65F2652E4EB5F8004EAFA3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E65F2492E4EB5F3004EAFA3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E65F2502E4EB5F4004EAFA3;
			remoteInfo = DeepBoard;
		};
		8E65F26F2E4EB5F8004EAFA3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E65F2492E4EB5F3004EAFA3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E65F2502E4EB5F4004EAFA3;
			remoteInfo = DeepBoard;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8E65F2512E4EB5F4004EAFA3 /* DeepBoard.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DeepBoard.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8E65F2642E4EB5F8004EAFA3 /* DeepBoardTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DeepBoardTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8E65F26E2E4EB5F8004EAFA3 /* DeepBoardUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DeepBoardUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8E651E022E4EB797004EAFA3 /* Packages */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Packages;
			sourceTree = "<group>";
		};
		8E651E1B2E4EBB69004EAFA3 /* ThirdParty */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ThirdParty;
			sourceTree = "<group>";
		};
		8E65F2532E4EB5F4004EAFA3 /* DeepBoard */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DeepBoard;
			sourceTree = "<group>";
		};
		8E65F2672E4EB5F8004EAFA3 /* DeepBoardTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DeepBoardTests;
			sourceTree = "<group>";
		};
		8E65F2712E4EB5F8004EAFA3 /* DeepBoardUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DeepBoardUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8E65F24E2E4EB5F4004EAFA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E65F2612E4EB5F8004EAFA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E65F26B2E4EB5F8004EAFA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8E65F2482E4EB5F3004EAFA3 = {
			isa = PBXGroup;
			children = (
				8E65F2532E4EB5F4004EAFA3 /* DeepBoard */,
				8E65F2672E4EB5F8004EAFA3 /* DeepBoardTests */,
				8E65F2712E4EB5F8004EAFA3 /* DeepBoardUITests */,
				8E65F2522E4EB5F4004EAFA3 /* Products */,
				8E651E022E4EB797004EAFA3 /* Packages */,
				8E651E1B2E4EBB69004EAFA3 /* ThirdParty */,
			);
			sourceTree = "<group>";
		};
		8E65F2522E4EB5F4004EAFA3 /* Products */ = {
			isa = PBXGroup;
			children = (
				8E65F2512E4EB5F4004EAFA3 /* DeepBoard.app */,
				8E65F2642E4EB5F8004EAFA3 /* DeepBoardTests.xctest */,
				8E65F26E2E4EB5F8004EAFA3 /* DeepBoardUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8E65F2502E4EB5F4004EAFA3 /* DeepBoard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E65F2782E4EB5F8004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoard" */;
			buildPhases = (
				8E65F24D2E4EB5F4004EAFA3 /* Sources */,
				8E65F24E2E4EB5F4004EAFA3 /* Frameworks */,
				8E65F24F2E4EB5F4004EAFA3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8E651E022E4EB797004EAFA3 /* Packages */,
				8E651E1B2E4EBB69004EAFA3 /* ThirdParty */,
				8E65F2532E4EB5F4004EAFA3 /* DeepBoard */,
			);
			name = DeepBoard;
			packageProductDependencies = (
			);
			productName = DeepBoard;
			productReference = 8E65F2512E4EB5F4004EAFA3 /* DeepBoard.app */;
			productType = "com.apple.product-type.application";
		};
		8E65F2632E4EB5F8004EAFA3 /* DeepBoardTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E65F27B2E4EB5F8004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardTests" */;
			buildPhases = (
				8E65F2602E4EB5F8004EAFA3 /* Sources */,
				8E65F2612E4EB5F8004EAFA3 /* Frameworks */,
				8E65F2622E4EB5F8004EAFA3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E65F2662E4EB5F8004EAFA3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E65F2672E4EB5F8004EAFA3 /* DeepBoardTests */,
			);
			name = DeepBoardTests;
			packageProductDependencies = (
			);
			productName = DeepBoardTests;
			productReference = 8E65F2642E4EB5F8004EAFA3 /* DeepBoardTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8E65F26D2E4EB5F8004EAFA3 /* DeepBoardUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E65F27E2E4EB5F8004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardUITests" */;
			buildPhases = (
				8E65F26A2E4EB5F8004EAFA3 /* Sources */,
				8E65F26B2E4EB5F8004EAFA3 /* Frameworks */,
				8E65F26C2E4EB5F8004EAFA3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E65F2702E4EB5F8004EAFA3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E65F2712E4EB5F8004EAFA3 /* DeepBoardUITests */,
			);
			name = DeepBoardUITests;
			packageProductDependencies = (
			);
			productName = DeepBoardUITests;
			productReference = 8E65F26E2E4EB5F8004EAFA3 /* DeepBoardUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8E65F2492E4EB5F3004EAFA3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					8E65F2502E4EB5F4004EAFA3 = {
						CreatedOnToolsVersion = 16.2;
					};
					8E65F2632E4EB5F8004EAFA3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E65F2502E4EB5F4004EAFA3;
					};
					8E65F26D2E4EB5F8004EAFA3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E65F2502E4EB5F4004EAFA3;
					};
				};
			};
			buildConfigurationList = 8E65F24C2E4EB5F3004EAFA3 /* Build configuration list for PBXProject "DeepBoard" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8E65F2482E4EB5F3004EAFA3;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 8E65F2522E4EB5F4004EAFA3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8E65F2502E4EB5F4004EAFA3 /* DeepBoard */,
				8E65F2632E4EB5F8004EAFA3 /* DeepBoardTests */,
				8E65F26D2E4EB5F8004EAFA3 /* DeepBoardUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8E65F24F2E4EB5F4004EAFA3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E65F2622E4EB5F8004EAFA3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E65F26C2E4EB5F8004EAFA3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8E65F24D2E4EB5F4004EAFA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E65F2602E4EB5F8004EAFA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E65F26A2E4EB5F8004EAFA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8E65F2662E4EB5F8004EAFA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E65F2502E4EB5F4004EAFA3 /* DeepBoard */;
			targetProxy = 8E65F2652E4EB5F8004EAFA3 /* PBXContainerItemProxy */;
		};
		8E65F2702E4EB5F8004EAFA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E65F2502E4EB5F4004EAFA3 /* DeepBoard */;
			targetProxy = 8E65F26F2E4EB5F8004EAFA3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8E65F2762E4EB5F8004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8E65F2772E4EB5F8004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8E65F2792E4EB5F8004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DeepBoard/DeepBoard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DeepBoard/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8E65F27A2E4EB5F8004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DeepBoard/DeepBoard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DeepBoard/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		8E65F27C2E4EB5F8004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DeepBoard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DeepBoard";
			};
			name = Debug;
		};
		8E65F27D2E4EB5F8004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DeepBoard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DeepBoard";
			};
			name = Release;
		};
		8E65F27F2E4EB5F8004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = DeepBoard;
			};
			name = Debug;
		};
		8E65F2802E4EB5F8004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = DeepBoard;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8E65F24C2E4EB5F3004EAFA3 /* Build configuration list for PBXProject "DeepBoard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E65F2762E4EB5F8004EAFA3 /* Debug */,
				8E65F2772E4EB5F8004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E65F2782E4EB5F8004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E65F2792E4EB5F8004EAFA3 /* Debug */,
				8E65F27A2E4EB5F8004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E65F27B2E4EB5F8004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E65F27C2E4EB5F8004EAFA3 /* Debug */,
				8E65F27D2E4EB5F8004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E65F27E2E4EB5F8004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E65F27F2E4EB5F8004EAFA3 /* Debug */,
				8E65F2802E4EB5F8004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8E65F2492E4EB5F3004EAFA3 /* Project object */;
}
