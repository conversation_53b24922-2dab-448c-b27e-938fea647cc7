# ChessKit 1.4.0
Released Thursday, August 1, 2025.

## Bug Fixes

### Multi-Game Parser Edge Cases
* **FIXED**: `splitPGNIntoGames(_:)` now correctly parses games without PGN tag headers
  * **Issue**: Games like `"1. e4 e5 *\n1. d4 Nf6 *"` were incorrectly parsed as single game
  * **Root Cause**: Regex pattern `(\s*(?=\[)|$)` only recognized tag separators, not move-number separators
  * **Solution**: Enhanced pattern to `(\s*(?=\[)|$|\s*(?=\d+\.))` recognizing three boundary types:
    - Tag-separated: `* [Event "..."]` (result followed by PGN tag)
    - File-end: `*` (result at end of input)
    - Move-separated: `* 1. e4` (result followed by move number)
* **NEW**: Added comprehensive test `testParseMultipleGames_NoTagsMultipleGames()` covering no-tags scenarios
* **ENHANCED**: Improved boundary detection reliability for edge cases and malformed PGN content

### Parser Robustness
* **IMPROVED**: Multi-game parsing now handles all three common game separation patterns
* **TESTED**: All existing 14+ multi-game parser tests continue to pass with enhanced pattern
* **COMPATIBLE**: Backward compatibility maintained for existing single-game parsing functionality

## Technical Implementation

### Regex Pattern Enhancement
* **Pattern**: Updated game boundary detection from `(1-0|0-1|1/2-1/2|\*)(\s*(?=\[)|$)` 
* **To**: `(1-0|0-1|1/2-1/2|\*)(\s*(?=\[)|$|\s*(?=\d+\.))`
* **Impact**: Enables parsing of consecutive games without PGN headers
* **Performance**: No performance impact, maintains O(n) linear parsing complexity

### Test Coverage
* **Added**: Edge case test for no-tags multi-game scenarios
* **Verified**: 100% pass rate across all existing multi-game parser tests  
* **Coverage**: Comprehensive validation of boundary detection patterns

# ChessKit 1.3.0
Released Thursday, July 31, 2025.

## New Features

### Multi-Game PGN Parsing
* **NEW**: `PGNParser.parseMultiple(games:)` method for parsing multiple games from a single PGN string
  * **Functionality**: Automatically detects game boundaries using result markers (`1-0`, `0-1`, `1/2-1/2`, `*`)
  * **Format Support**: Handles various line ending formats (Windows `\r\n`, Mac `\r`, Unix `\n`)
  * **Error Resilience**: Invalid games are filtered out while preserving valid games
  * **Performance**: Efficient regex-based parsing optimized for large PGN collections
* **NEW**: `splitPGNIntoGames(_:)` private method for robust game boundary detection
  * Uses advanced regex patterns to identify game separators
  * Handles edge cases like missing results or malformed game boundaries
  * Maintains game content integrity during splitting process

### Intelligent Result Determination  
* **ENHANCED**: `PGNParser.convert(game:)` method now includes intelligent result determination
  * **Priority Logic**: Existing non-empty result tags take precedence over analysis
  * **Checkmate Detection**: Analyzes `CheckState` of the final move to determine outcomes
    - `checkmate` → Returns `1-0` or `0-1` based on which side delivered checkmate
    - `stalemate` → Returns `1/2-1/2` for draw by stalemate
    - `check` or `none` → Returns `*` for ongoing games
* **NEW**: `determineGameResult(for:)` private method implementing intelligent analysis
  * Examines final position and last move's check state
  * Robust fallback handling for edge cases and indeterminate positions
  * Maintains backward compatibility with existing result tag behavior

## Technical Implementation

### Multi-Game Parsing Architecture
* **Regex Engine**: Uses `NSRegularExpression` with pattern `(1-0|0-1|1/2-1/2|\*)(\s*(?=\[)|$)` for boundary detection
* **String Processing**: Normalizes line endings before parsing to ensure consistent behavior
* **Memory Efficiency**: Processes games incrementally without loading entire collection into memory
* **Error Handling**: Graceful degradation when encountering malformed PGN content

### Result Analysis System
* **Position Analysis**: Leverages existing `MoveTree.lastMainVariationIndex` for final position access
* **State Examination**: Analyzes `MetaMove.checkState` property for game termination conditions
* **Fallback Logic**: Comprehensive error handling for indeterminate or incomplete games
* **Integration**: Seamlessly integrates with existing PGN generation workflow

## Testing & Quality Assurance

### Comprehensive Test Coverage
* **Multi-Game Parsing Tests**: 13 test methods covering various scenarios
  - Basic functionality (2, 3, single game parsing)  
  - Edge cases (empty strings, invalid PGN, mixed formats)
  - Format compatibility (Windows/Mac line endings)
  - Performance testing (100+ game collections)
  - Consistency validation with single-game parser
* **Result Determination Tests**: 3 test methods validating intelligent result logic
  - Checkmate detection and winner determination
  - Existing result tag preservation
  - Empty result tag handling with fallback to ongoing (`*`)

### Performance Validation
* **Large Collection Support**: Tested with 100+ game PGN files
* **Parsing Speed**: Average 0.177 seconds for 100-game collection parsing
* **Memory Usage**: Optimized memory footprint with incremental processing
* **Compatibility**: All existing functionality preserved, zero breaking changes

## Migration Notes

### From ChessKit 1.2.2
* **No Breaking Changes**: All existing APIs remain fully functional
* **New APIs**: `parseMultiple(games:)` method is additive, existing `parse(game:)` unchanged
* **Behavioral Enhancement**: `convert(game:)` now includes intelligent result determination but maintains backward compatibility
* **Import Changes**: No changes to import statements or initialization code required

### Usage Updates
```swift
// Existing code continues to work unchanged
let game = PGNParser.parse(game: pgnString)  // ✅ Still works

// New multi-game capability available
let games = PGNParser.parseMultiple(games: multiGamePGN)  // ✅ New feature

// Enhanced result handling (automatic)
let pgn = PGNParser.convert(game: game)  // ✅ Now includes intelligent results
```

---

# ChessKit 1.2.2
Released Saturday, July 26, 2025.

## Performance & Memory Optimizations

### UndoState Structure Optimization
* **OPTIMIZED**: Removed unnecessary fields from UndoState structures to reduce memory footprint
  * **Removed**: `newMoveIndex` and `newMovePath` fields from `AddMoveUndoState` and `OverwriteUndoState`
  * **Reasoning**: These fields can be derived from parent-child relationships using structural logic
  * **Impact**: Reduced memory usage while maintaining full functionality and reliability
* **IMPROVED**: Made `parentPath` non-optional in key UndoState structures for enhanced reliability
  * Eliminates optional unwrapping overhead and simplifies error handling
  * Ensures consistent path-based backup for robust index resolution

### API Encapsulation Improvements
* **NEW**: Encapsulated index resolution methods in UndoState structures
  * `AddMoveUndoState.resolvedParentIndex(in:)` - robust parent index resolution with fallback
  * `AddMoveUndoState.newMoveIndexToRemove(in:)` - structural move identification for undo operations
  * `OverwriteUndoState.resolvedParentIndex(in:)` - consistent parent resolution across state types
  * `OverwriteUndoState.newMoveIndexToRemove(in:)` - specialized logic for overwrite undo operations
* **ELIMINATED**: Repetitive index resolution code across modules
  * Removed duplicate logic from MoveTree.swift, Game.swift, and GameSession.swift
  * Consolidated complex index resolution patterns into clean, reusable methods
  * Improved code maintainability and reduced potential for implementation inconsistencies

### Technical Implementation Details
* **ENHANCED**: Correct undo priority logic implementation
  * Children-first, then-next priority order to properly reverse add operation sequence
  * Uses structural parent-child relationships instead of SAN string comparison
  * Maintains robust index resolution with primary-index-first, path-based-fallback approach
* **VERIFIED**: Cross-module integration with MacChessBase GameSession
  * Updated GameSession.swift to use new encapsulated UndoState methods
  * Maintains backward compatibility while leveraging optimized API

## Testing & Quality Assurance
* **MAINTAINED**: All 183 tests continue to pass with optimized structures
* **VERIFIED**: Both ChessKit library and MacChessBase application compile successfully
* **ENHANCED**: Memory efficiency improvements with no functional regressions

---

# ChessKit 1.2.1
Released Saturday, July 26, 2025.

## Bug Fixes

### Critical Undo/Redo System Fix
* **FIXED**: Consecutive redo operations failing after multiple undo operations
  * **Root Cause**: `Game.makeWithUndo` was calling regular `make()` method and manually creating `AddMoveUndoState` without proper path backup fields
  * **Solution**: Updated `Game.makeWithUndo` to use `moves.addWithUndo()` for proper path-based backup in undo states
  * **Impact**: All `AddMoveUndoState` objects now include `parentPath` and `newMovePath` fields for robust index resolution during consecutive operations
  * **Testing**: Added comprehensive consecutive operation tests (`testConsecutiveUndoRedoNoDelay`, `testRapidUndoRedoCycle`)

### Enhanced Path-Based Index Resolution
* **IMPROVED**: Undo/redo operations now properly handle index invalidation during consecutive operations
  * When indices become invalid during rapid undo/redo cycles, the system falls back to path-based node resolution
  * Ensures reliable operation restoration even when the tree structure changes between operations
  * Maintains backward compatibility with existing integer-based indexing

## Testing & Quality Assurance
* **ENHANCED**: All existing tests continue to pass with improved undo state system
* **VERIFIED**: Real-world consecutive redo operations now work correctly in application usage
* **MAINTAINED**: No breaking changes to public APIs

---

# ChessKit 1.2.0
Released Friday, July 26, 2025.

## Major Features

### Complete Undo/Redo System
* **NEW**: Comprehensive undo/redo functionality across the entire ChessKit library
  * 7 specialized undo state types: `AddMoveUndoState`, `DeleteMoveUndoState`, `DeleteBeforeMoveUndoState`, `PromoteVariationUndoState`, `PromoteToMainUndoState`, `OverwriteUndoState`, `EditMoveUndoState`
  * MoveTree-level undo/redo operations: `addWithUndo()`, `deleteWithUndo()`, `promoteWithUndo()`, etc.
  * Game-level integration with position management and PGN tag synchronization
  * 679 lines of comprehensive test coverage ensuring reliability
* **USE CASES**: Professional chess analysis, educational tools, error recovery
* **PERFORMANCE**: Efficient state capture and restoration with minimal memory overhead

### Enhanced MoveTree Indexing System
* **IMPROVED**: Maintained reliable integer-based indexing system (0, 1, 2, ...)
  * Ensures compatibility with undo/redo operations
  * Preserves familiar sequential indexing for predictable behavior
  * All existing code continues to work without modification
* **COMPATIBILITY**: No breaking changes to index system
* **BENEFITS**: Reliable indexing that works seamlessly with undo/redo operations

### Architecture Refactoring  
* **IMPROVED**: Separated game state management from UI logic
  * Core game state moved from `ChessGameViewModel` to `GameSession`
  * Clean architecture with better separation of concerns
  * Enhanced maintainability and testability
* **COMPATIBILITY**: All existing APIs maintained, internal refactoring only

### Bug Fixes
* **FIXED**: Variation navigation after architecture refactoring
  * Problem: `selectVariation()` method was directly setting `currentMoveIndex` instead of using `GameSession`
  * Solution: Updated to use `session?.goToMove(at: option.index)` for proper state management
  * Impact: Right-click variation selection now correctly updates game state and board position

## Testing & Quality Assurance
* **IMPROVED**: All 175 tests passing with enhanced architecture
* **MAINTAINED**: Familiar integer indexing requires no test modifications
* **ENHANCED**: Comprehensive test coverage for undo/redo functionality

---

# ChessKit 1.1.0
Released Monday, July 13, 2025.

### Bug Fixes (Latest Update)
* **FIXED**: Move notation display issues for games starting with black to move
  * Simplified move number tracking logic eliminates edge cases with custom starting positions
  * Fixed complex move number calculations in ChessGameViewModel.buildAllMovesWithVariations()
* **FIXED**: PGN export corrections for variation formatting
  * Corrected black move number display logic (e.g., "1..." after variation starts)
  * Improved PGN compliance for complex nested variation structures
* **FIXED**: Delete before move operation cleanup
  * Removed debug print statements and improved node update ordering
  * Enhanced robustness of tree manipulation operations

### Overwrite API
* **NEW**: Native overwrite functionality for efficient move replacement
  * `MoveTree.overwrite(move:toParentIndex:)`: Replace moves and all subsequent variations with a new move
  * `Game.overwrite(move:from:)`: Game-level overwrite with position management
  * `Game.overwrite(move:from:)` string overload: Convenient string-based move overwriting with SAN parsing
  * Returns new move index for seamless integration with existing workflows
  * Graceful error handling - returns original index on failure (invalid moves, invalid indices)
  * Leverages existing `deleteAllAfter` and `add` operations for robustness
* **NEW**: Comprehensive test coverage with 255+ lines of tests across GameTests and MoveTreeTests
* **USE CASES**: Move replacement workflows, analysis optimization, educational tools
* **PERFORMANCE**: Minimal overhead - leverages existing efficient deletion and addition operations

### Enhanced Move Tree Editing
* **NEW**: "Delete Before Move" functionality in `MoveTree` and `Game`
  * `MoveTree.deleteBeforeMove(at:)`: Removes all moves before a target move, making it the new starting position
  * `Game.deleteBeforeMove(at:)`: Game-level support with automatic FEN and SetUp tag updates
  * Automatically updates head node with previous position metadata
  * Preserves target move and all subsequent variations
  * Updates positions dictionary to maintain game state consistency
* **IMPROVED**: Move tree restructuring capabilities for advanced game editing workflows
* **NEW**: ChessGameViewModel integration with "Delete Before Move" UI support

---

# ChessKit 1.1.0 (Previous Release)
Released Monday, July 8, 2025.

### Enhanced Move Tree Editing
* **NEW**: "Delete Before Move" functionality in `MoveTree` and `Game`
  * `MoveTree.deleteBeforeMove(at:)`: Removes all moves before a target move, making it the new starting position
  * `Game.deleteBeforeMove(at:)`: Game-level support with automatic FEN and SetUp tag updates
  * Automatically updates head node with previous position metadata
  * Preserves target move and all subsequent variations
  * Updates positions dictionary to maintain game state consistency
* **IMPROVED**: Move tree restructuring capabilities for advanced game editing workflows
* **NEW**: ChessGameViewModel integration with "Delete Before Move" UI support

### Enhanced FEN Validation
* **NEW**: Comprehensive FEN (Forsyth-Edwards Notation) validation system in `FENParser`
* **NEW**: Complete chess rule validation including:
  * King count validation (exactly one king per side required)
  * Pawn placement validation (no pawns on ranks 1 or 8)
  * Piece count validation (maximum 16 pieces per side, 8 pawns per side)
  * Castling rights logic validation (kings and rooks must be in correct positions)
  * En passant target validation (proper pawn positions and turn consistency)
  * Check logic validation (non-active side's king cannot be in check)
* **NEW**: Advanced syntax validation:
  * Exactly 6 fields validation with proper field format checking
  * Piece placement validation (8 ranks, valid characters, correct square counts)
  * No consecutive numbers in rank notation
  * Active color validation (w/b only)
  * Castling rights validation (KQkq character set, no duplicates)
  * En passant coordinate validation (ranks 3 and 6 only)
  * Move counter validation (halfmove ≥ 0, fullmove ≥ 1)

### Position Editor Integration
* **IMPROVED**: Position editor now uses comprehensive FEN validation
* **NEW**: Validation occurs only on position confirmation, allowing temporary invalid states during editing
* **NEW**: User-friendly error handling with clear "Invalid Position!" alerts
* **NEW**: Real-time FEN validation status indicator in position editor UI
* **NEW**: Initial move number control with stepper interface for fullmove number editing
* **NEW**: Import error handling with "Invalid PGN or FEN" alerts for failed clipboard imports
* **IMPROVED**: Enhanced position editing workflow with robust validation backend
* **IMPROVED**: Larger position editor interface with better piece selection controls

### Backward Compatibility
* **MAINTAINED**: All existing APIs continue to work unchanged
* **IMPROVED**: Existing `Position(fen:)` calls now benefit from enhanced validation
* **MAINTAINED**: Same return behavior (nil for invalid FEN) with much more thorough checking

### Technical Improvements
* Enhanced security for position validation in chess applications
* More reliable FEN parsing for complex position setups
* Better error detection for malformed chess positions
* Improved reliability for chess analysis and position editing tools
* Better move number handling in game notation display

# ChessKit 1.0.0
Released Monday, July 7, 2025.

### Major Breaking Changes - Complete Architecture Redesign
This is a complete rewrite of ChessKit's core architecture to support advanced chess features and editing capabilities.

#### Move Structure Redesign
* **BREAKING**: `Move` struct has been completely redesigned and split into two components:
  * `MetaMove`: Contains the core move information (piece, start, end squares, checkState, etc.)
  * `PositionComment`: Rich comment system supporting text, time annotations, and visual annotations
  * `Move` now contains an optional `MetaMove` and a `PositionComment`
  * This allows for head nodes (starting positions) that can have comments but no actual move

#### Enhanced Assessment System
* **BREAKING**: `Move.Assessment` renamed to `MetaMove.Assessment` with extensive additions:
  * Expanded from 9 basic assessments to 135+ standardized Numerical Annotation Glyphs (NAGs)
  * Separated move assessments (!, ?, !!, ??, etc.) from position assessments (+/-, =, ∞, etc.)
  * Added `moveAssessment` and `positionAssessment` properties to `MetaMove`
  * Removed single `assessment` and `comment` properties from old `Move` structure

#### Advanced Comment System
* **NEW**: Rich comment system with structured support for:
  * Text comments
  * Time annotations (`[%clk]` for remaining time, `[%emt]` for time spent)
  * Visual annotations (`[%csl]` for square highlights, `[%cal]` for arrows)
  * Color-coded annotations (Red, Green, Blue)
  * Proper parsing and serialization of complex nested comment structures

#### MoveTree Complete Redesign
* **BREAKING**: Removed `variation` property-based indexing system
* **NEW**: Integer-based hash indexing system starting from 0
* **NEW**: `headNode` concept for representing the starting position
* **NEW**: Advanced move tree editing capabilities:
  * `delete(at:)`: Remove moves and all descendants recursively
  * `promote(index:)`: Promote variations to higher priority
  * `promoteToMainVariation(index:)`: Promote any variation to become the main line
  * `isOnMainVariation(index:)`: Check if a move is on the main variation
  * `getVariationRootNode(for:)`: Find the root of any variation
* **BREAKING**: Changed from `MoveTree.Index` to simple `Int` (typealias `MoveIndex`)
* **NEW**: Efficient variation management without expensive recalculation
* **NEW**: Support for complex editing operations while maintaining tree integrity

#### PGN Parser Complete Rewrite
* **BREAKING**: Complete tokenizer-based rewrite of `PGNParser`
* **NEW**: Robust token-based parsing supporting:
  * Complex nested comments with proper bracket handling
  * Time annotations (`[%clk]`, `[%emt]`)
  * Visual annotations (`[%csl]`, `[%cal]`)
  * All 135+ Numerical Annotation Glyphs
  * Better variation parsing and handling
* **IMPROVED**: More reliable parsing of complex PGN structures
* **IMPROVED**: Better error handling (TODO: Error functionality not yet complete)

#### API Changes
* **BREAKING**: `SANParser.parse(move:)` → `SANParser.parse(metaMove:)`
* **BREAKING**: `EngineLANParser.convert(move:)` → `EngineLANParser.convert(metaMove:)`
* **NEW**: `Move.pgnDescription` for PGN representation
* **NEW**: `MetaMove.displayDescription` for UI display
* **NEW**: Rich assessment descriptions and categorization
* **NEW**: Comprehensive move tree navigation and editing methods

### Migration Guide
This is a major breaking release. Key changes needed:
1. Replace `Move` usage with appropriate `MetaMove` + `PositionComment` combination
2. Update move tree indexing from `MoveTree.Index` to `Int`
3. Replace assessment/comment properties with new structured approach
4. Update PGN parsing calls to use new API
5. Update SAN/LAN parser calls for new method names

### Technical Improvements
* More efficient memory usage with integer-based indexing
* Faster move tree operations without variation recalculation
* Better support for chess analysis and editing applications
* Comprehensive test coverage for new functionality

# [previous unreleased]

# ChessKit 0.14.0
Released Tuesday, June 3, 2025.

### New Features
* Add `willPromote()` to `BoardDelegate` (by [@Amir-Zucker](https://github.com/Amir-Zucker)).
  * Called when pawn reaches last rank but before `Board.completePromotion()` is called.

### Improvements
* `PGNParser` now parses `*` game results (indicating game in progress, abandoned, unknown, etc.).
* Add `CustomStringConvertible` conformance to `Piece`, `Piece.Color`, and `Piece.Kind`.
* Migrate all unit tests (expect performance tests) from `XCTest` to `Swift Testing`.

### Bug Fixes
* Fix issue where `MoveTree.endIndex` is not properly updated after the first half move (by [@Amir-Zucker](https://github.com/Amir-Zucker)).
* Fix issue where `BoardDelegate.didPromote()` was called before and after promotion (by [@Amir-Zucker](https://github.com/Amir-Zucker)).
* Fix issue where pawns on the starting rank could hop over pieces in front of them, see [Issue #49](https://github.com/chesskit-app/chesskit-swift/issues/49) (by [@joee-ca](https://github.com/joee-ca)).
* Fix `SANParser` (and `PGNParser`) accepting invalid SANs when they started with a valid SAN (e.g. `"e44"` which starts with the valid `"e4"`).

### Breaking Changes
* `Game(pgn:)` is no longer a failable initializer, it will now always create a non-`nil` `Game`.

# ChessKit 0.13.0
Released Thursday, October 3, 2024.

### New Features
* `BoardDelegate` now notifies when king is in check, and provides the color of the checked king, see [Issue #38](https://github.com/chesskit-app/chesskit-swift/issues/38).
* `Move.checkState` and `Move.disambiguation` are now publicly accessible, see [Issue #38](https://github.com/chesskit-app/chesskit-swift/issues/38).

### Technical Changes
* Enable Swift 6 language mode package-wide.
* `Game` and `MoveTree` are now `Sendable` types.

# ChessKit 0.12.1
Released Wednesday, September 11, 2024.

### Bug Fixes
* Fix `MoveTree.fullVariation(for:)` returning blank array when `.minimum` is passed as the index.
  * If this index is passed, it will automatically start from the next (valid) index and return the moves for that index.
  * In practice this will mean the main variation will be returned (starting from white's first move).

# ChessKit 0.12.0
Released Wednesday, August 21, 2024.

### Improvements
* Conform more types to `Hashable` such as `Game` and `MoveTree`.
* `Game.Tag` now publicly exposes `name`.
* `Game.Tags` is now `Hashable` and `Sendable`.

# ChessKit 0.11.0
Released Monday, August 5, 2024.

### New Features
* A draw result is now published by `BoardDelegate` when the board encounters a threefold repetition (by [@joee-ca](https://github.com/joee-ca)).
* Convenience directional properties added to `Square` such as `up`, `down`, `left`, and `right` to obtain squares in relation to the given `Square`.

### Bug Fixes
* `File.init(_ number: Int)` now correctly bounds invalid values.
  * i.e. Values less than 1 become `File.a` and values greater than 8 become `File.h`.

### Technical Changes
* Test coverage has been improved.
* Parsers (`EngineLANParser`, `FENParser`, `PGNParser`, `SANParser`) have been converted from classes to caseless enums.
  * This should have no effect on existing code since the class versions had private initializers.

# ChessKit 0.10.0
Released Friday, June 21, 2024.

### Improvements
* Update tools version to Swift 5.9 (requires Xcode 15.0 or greater).
* Conform to Swift strict concurrency and add `Sendable` conformance to most objects

### Breaking Changes
* `Game` is now a `struct` and no longer conforms to `ObservableObject`.
  * If observation semantics are required, consider using `didSet` property observers or an object that utilizes the `@Observable` macro.

# ChessKit 0.9.0
Released Saturday, June 15, 2024.

### Improvements
* `MoveTree` now conforms to `BidirectionalCollection`, allowing for more standard collection-based semantics in Swift.
  * Should not affect any existing functionality or API usage.
  * Several methods on `MoveTree` have been deprecated in favor of their `Collection` counterparts:
    * `previousIndex(for:)` → `index(before:)` / `hasIndex(before:)`
    * `nextIndex(for:)` → `index(after:)` / `hasIndex(after:)`
    * `move(at:)` → `subscript(_:)` (e.g. `tree[index]`)
* `MoveTree.annotate()` now optionally returns the `Move` object after annotation.
* `MoveTree.path()` now returns tuple with named parameters (`direction` and `index`).

### Bug Fixes
* Removed `CustomDebugStringConvertible` conformance from `Bitboard` to avoid affecting all `UInt64` debug prints.
  * To print the string representation of `Bitboard` use `Bitboard.chessString()`.

# ChessKit 0.8.0
Released Friday, June 7, 2024.

### Improvements
* Add support for draw by insufficient material (by [@joee-ca](https://github.com/joee-ca)).
  * Once this condition is reached `.draw(.insufficientMaterial)` will be published via the `BoardDelegate.didEnd(with:)` method.
* Add unicode variant selector when printing black pawn icon to avoid displaying emoji (by [@joee-ca](https://github.com/joee-ca)).

### Bug Fixes
* Fix issue where king could castle through other pieces (by [@TigranSaakyan](https://github.com/TigranSaakyan)).

# ChessKit 0.7.1
Released Monday, May 6, 2024.

* Fix `MoveTree.previousIndex(for:)` when provided index is one after `minimumIndex`.

# ChessKit 0.7.0
Released Monday, April 29, 2024.

### Improvements
* Add `startingIndex` and `startingPosition` to `Game`.
  * `startingIndex` takes into account the `sideToMove` of `startingPosition`.

### Bug Fixes
* Fix rare en passant issue that could allow the king to be left in check, see [Issue #18](https://github.com/chesskit-app/chesskit-swift/issues/18).

# ChessKit 0.6.0
Released Friday, April 19, 2024.

### Improvements
* Enable `chesskit-swift` to run on oldest platform possible without code changes.
  * Now works on iOS 13+, macOS 10.15+, tvOS 13+, watchOS 6+.
* Annotations on moves in the `MoveTree` can now also be updated via `Game.annotate(moveAt:assessment:comment:)`.

### Bug Fixes
* Fix `MoveTree` not properly publishing changes via `Game`.
* Fix `Board.EndResult.repetition` spelling.
  * This isn't made available yet but will be implemented in an upcoming release.

# ChessKit 0.5.0
Released Sunday, April 14, 2024.

### Improvements
* PGN parsing now supports tag pairs (for example `[Event "Name"]`) located at the top of the PGN format, see [Issue #8](https://github.com/chesskit-app/chesskit-swift/issues/8).

### Bug Fixes
* Fix issue where king is allowed to castle in check, see [Issue #11](https://github.com/chesskit-app/chesskit-swift/issues/11).

### Breaking Changes
* Remove `color` parameter from `Move.init(san:color:position:)` initializer.
  * It was not being used, can be removed from any initializer call where it was included.
  * The new initializer is simply `Move.init(san:position:)`.

# ChessKit 0.4.0
Released Saturday, April 13, 2024.

### Improvements
* `Board` move calculation and validation performance has greatly increased.
  * Performance has improved by over 250x when simulating a full game using `Board`.
  * Underlying board representation has been replaced with much faster bitboard structures and algorithms.
* Add `CustomStringConvertible` conformance to `Board` and `Position` to allow for printing chess board representations, useful for debugging.
* Add `ChessKitConfiguration` with static configuration properties for the package.
  * Currently the only option is `printMode` to determine how pieces should be represented when printing `Board` and `Position` objects (see previous item).

### Breaking Changes
* `EnPassant` has been made an `internal struct`. It is used interally by `Position` and `Board`.

### Deprecations
* `Position.toggleSideToMove()` is now private and handled automatically when calling `move()`. The public-facing `toggleSideToMove()` has been deprecated.

# ChessKit 0.3.2
Released Saturday, December 2, 2023.

### Fixes
* Made `file` and `rank` public properties of `Square`.

# ChessKit 0.3.1
Released Friday, November 24, 2023.

### Improvements
* Add `CaseIterable` conformance to several `Piece` and `Square` enums:
    * `Piece.Color`
    * `Piece.Kind`
    * `Square.Color`

# ChessKit 0.3.0
Released Wednesday, June 21, 2023.

### New Features
* Add `future(for:)` and `fullVariation(for:)` methods to `MoveTree`.
	* `future(for:)` returns the future moves for a given
index.
	* `fullVariation(for:)` returns the sum of `history(for:)` and `future(for:)`.

### Improvements
* Simplify `PGNElement` to just contain a single `.move` case.
	* i.e. `.whiteMove` and `blackMove` have been removed and consolidated.

### Fixes
* Fix behavior of `previousIndex(for:)` and `nextIndex(for:)` in `MoveTree`.
	* Especially when the provided `index` is equal to `.minimum`.

# ChessKit 0.2.0
Released Wednesday, May 31, 2023.

### New Features
* `MoveTree` and `MoveTree.Index` objects to track move turns and variations.
    * `Game.moves` is now a `MoveTree` object instead of `[Int: MovePair]`
    * `MoveTree.Index` includes piece color and variation so it can be used to directly identify any single move within a game
    * Use the properties and functions of `MoveTree` to retrieve moves within the tree as needed

* `make(move:index:)` and `make(moves:index:)` with ability to make moves on `Game` with SAN strings for convenience
    * For example: `game.make(moves: ["e4", "e5"])`

* `PGNParser.convert(game:)` now returns the PGN string for a given game, including variations.
    * Note: `PGNParser.parse(pgn:)` still does not work with variations, this is coming in a future update.

* `Game.positions` is now public
    * Contains a dictionary of all positions in the game by `MoveTree.Index`, including variations

### Removed
* `Game.annotateMove`
    * Modify `Move.assessment` and `Move.comment` directly instead
* `MovePair`
    * Use `Move` in conjuction with `MoveTree.Index` to track move indicies
* `color` parameter from `SANParser.parse()`
    * The color is now obtained from the `sideToMove` in the provided `position`

# ChessKit 0.1.2
Released Thursday, May 11, 2023.

* Add documentation for all public members
* Add default starting position for `Game` initializer
* Add ability to annotate moves via `Game`

# ChessKit 0.1.1
Released Wednesday, April 12, 2023.

* Downgrade required Swift version to 5.7
	* Allows use with Xcode 14.2 on GitHub Actions

# ChessKit 0.1.0
Released Tuesday, April 11, 2023.

* Initial release
