//
//  MoveTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class MoveTests: XCTestCase {

   // MARK: - MetaMove Tests

   func testMetaMoveSANInit() {
       let move = MetaMove(result: .move, piece: .init(.pawn, color: .white, square: .e4), start: .e2, end: .e4)
       let moveFromSAN = MetaMove(san: "e4", position: .standard)

       XCTAssertEqual(move, moveFromSAN)
   }

   func testMetaMoveInvalidSANInit() {
       XCTAssertNil(MetaMove(san: "e5", position: .standard))
   }

   func testMoveNotation() {
       let pawnD3 = MetaMove(result: .move, piece: Piece(.pawn, color: .white, square: .d3), start: .d2, end: .d3)
       XCTAssertEqual(pawnD3.san, "d3")
       XCTAssertEqual(pawnD3.lan, "d2d3")

       let bishopF4 = MetaMove(result: .move, piece: Piece(.bishop, color: .white, square: .f4), start: .c1, end: .f4)
       XCTAssertEqual(bishopF4.san, "Bf4")
       XCTAssertEqual(bishopF4.lan, "c1f4")
   }

   func testCaptureNotation() {
       let capturedPiece = Piece(.bishop, color: .black, square: .d5)
       let capturingPiece = Piece(.pawn, color: .white, square: .e4)
       let capture = MetaMove(result: .capture(capturedPiece), piece: capturingPiece, start: .e4, end: .d5)
       XCTAssertEqual(capture.san, "exd5")
       XCTAssertEqual(capture.lan, "e4d5")
   }

   func testCastlingNotation() {
       let shortCastle = MetaMove(result: .castle(.bK), piece: Piece(.king, color: .black, square: .e8), start: .e8, end: .g8)
       XCTAssertEqual(shortCastle.san, "O-O")
       XCTAssertEqual(shortCastle.lan, "e8g8")

       let longCastle = MetaMove(result: .castle(.bQ), piece: Piece(.king, color: .black, square: .e8), start: .e8, end: .c8, checkState: .checkmate)
       XCTAssertEqual(longCastle.san, "O-O-O#")
       XCTAssertEqual(longCastle.lan, "e8c8")
   }

   func testPromotionsNotation() {
       let pawn = Piece(.pawn, color: .white, square: .e8)
       let queen = Piece(.queen, color: .white, square: .e8)
       let rook = Piece(.rook, color: .white, square: .e8)

       var queenPromo = MetaMove(result: .move, piece: pawn, start: .e7, end: .e8)
       queenPromo.promotedPiece = queen
       XCTAssertEqual(queenPromo.san, "e8=Q")
       XCTAssertEqual(queenPromo.lan, "e7e8q")

       let capturedPiece = Piece(.bishop, color: .black, square: .f8)
       var rookCapturePromo = MetaMove(result: .capture(capturedPiece), piece: pawn, start: .e7, end: .f8, checkState: .check)
       rookCapturePromo.promotedPiece = rook
       XCTAssertEqual(rookCapturePromo.san, "exf8=R+")
       XCTAssertEqual(rookCapturePromo.lan, "e7f8r")
   }
   
   func testAssessments() {
       XCTAssertEqual(MetaMove.Assessment.good.notation, "!")
       XCTAssertEqual(MetaMove.Assessment.mistake.notation, "?")
       XCTAssertEqual(MetaMove.Assessment.brilliant.notation, "!!")
       XCTAssertEqual(MetaMove.Assessment.blunder.notation, "??")
   }

   // MARK: - PositionComment Tests

   func testPositionCommentParsing() {
       let comment = "This is a test comment."
       let clock = "[%clk 0:10:05]"
       let arrows = "[%cal Gd1e2,Rf3g5]"
       let highlights = "[%csl Gf3,Ge2]"

       let fullComment = "1. d4 { \(comment) } { \(clock) } { \(arrows)\(highlights) }"
       
       let game = PGNParser.parse(game: fullComment)
       let firstMoveIndex = game?.moves.mainVariationIndex(at: 0)
       let positionComment = firstMoveIndex != nil ? game?.moves.getNodeMove(index: firstMoveIndex!)?.positionComment : nil

       XCTAssertEqual(positionComment?.text, comment)
       XCTAssertEqual(positionComment?.timeAnnotations.remainingTime, "0:10:05")

       let expectedArrows = [
           Move.VisualAnnotations.Arrow(color: .green, from: .d1, to: .e2),
           Move.VisualAnnotations.Arrow(color: .red, from: .f3, to: .g5)
       ]
       XCTAssertEqual(positionComment?.visualAnnotations.arrows, expectedArrows)

       let expectedHighlights = [
           Move.VisualAnnotations.SquareHighlight(color: .green, square: .f3),
           Move.VisualAnnotations.SquareHighlight(color: .green, square: .e2)
       ]
       XCTAssertEqual(positionComment?.visualAnnotations.squareHighlights, expectedHighlights)

       // Initial position comment
       let initialComment = "{ \(comment) } { \(clock) } { \(arrows)\(highlights) }"
       let initialGame = PGNParser.parse(game: initialComment)
       let initialPositionComment = initialGame?.moves.getNodeMove(index: MoveTree.minimumIndex)?.positionComment

       XCTAssertEqual(initialPositionComment?.text, comment)
       XCTAssertEqual(initialPositionComment?.timeAnnotations.remainingTime, "0:10:05")
       XCTAssertEqual(initialPositionComment?.visualAnnotations.arrows, expectedArrows)
   }
   
   func testPositionCommentDescription() {
       let comment = "Another comment"
       let time = Move.TimeAnnotations(timeSpent: "00:00:02")
       let visual = Move.VisualAnnotations(
           squareHighlights: [.init(color: .blue, square: .a1)],
           arrows: [.init(color: .red, from: .h1, to: .h8)]
       )
       
       let positionComment = Move.PositionComment(text: comment, timeAnnotations: time, visualAnnotations: visual)
       
       let expected = "{ Another comment } { [%emt 00:00:02] } { [%csl Ba1] [%cal Rh1h8] }"
       XCTAssertEqual(positionComment.description, expected)
   }

   // MARK: - Move (Combined) Tests
   
   func testMovePgnDescription() {
       var metaMove = MetaMove(san: "Nf3", position: .standard)!
       metaMove.moveAssessment = .good
       metaMove.positionAssessment = .whiteSlightAdv
       
       let positionComment = Move.PositionComment(text: "Good move for white.")
       
       let move = Move(metaMove: metaMove, positionComment: positionComment)
       
       let expected = "Nf3 $1 $14 { Good move for white. }"
       XCTAssertEqual(move.pgnDiscription, expected)
   }
}
