//
//  SimpleMoveHashTest.swift
//  ChessKitTests
//

import XCTest
@testable import ChessKit

final class SimpleMoveHashTest: XCTestCase {

   func testBasicMoveHashOperations() {
       var tree = MoveTree()

       // Test basic tree construction with new API
       let startPosition = Position.standard
       let afterE4 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!

       let e4 = Move(metaMove: .init(san: "e4", position: startPosition)!)
       let e4Index = tree.add(move: e4, toParentIndex: MoveTree.minimumIndex)

       let e5 = Move(metaMove: .init(san: "e5", position: afterE4)!)
       let e5Index = tree.add(move: e5, toParentIndex: e4Index)

       // Verify basic functionality
       XCTAssertEqual(tree.allCount, 2) // e4, e5
       XCTAssertNotNil(tree.getNodeMove(index: e4Index))
       XCTAssertNotNil(tree.getNodeMove(index: e5Index))

       // Test index-based methods
       XCTAssertTrue(tree.isOnMainVariation(index: e4Index))
       XCTAssertTrue(tree.isOnMainVariation(index: e5Index))
   }

   func testBranchCreationAndPromotion() {
       var tree = MoveTree()

       let startPosition = Position.standard
       let afterE4 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!
       let afterE5 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2")!

       let e4 = Move(metaMove: .init(san: "e4", position: startPosition)!)
       let e4Index = tree.add(move: e4, toParentIndex: MoveTree.minimumIndex)

       let e5 = Move(metaMove: .init(san: "e5", position: afterE4)!)
       let e5Index = tree.add(move: e5, toParentIndex: e4Index)

       let nf3 = Move(metaMove: .init(san: "Nf3", position: afterE5)!)
       let nf3Index = tree.add(move: nf3, toParentIndex: e5Index)

       let d4 = Move(metaMove: .init(san: "d4", position: afterE5)!)
       let d4Index = tree.add(move: d4, toParentIndex: e5Index)

       // Initially, nf3 should be on main variation, d4 should not
       XCTAssertTrue(tree.isOnMainVariation(index: nf3Index))
       XCTAssertFalse(tree.isOnMainVariation(index: d4Index))

       // Check that we have variations from e5
       let variations = tree.variations(from: e5Index)
       XCTAssertEqual(variations.count, 1) // d4 should be a variation
       XCTAssertTrue(variations.contains(d4Index))

       // Promote d4 to main variation
       let success = tree.promoteToMainVariation(index: d4Index)
       XCTAssertTrue(success)

       // After promotion, d4 should be on main variation
       XCTAssertTrue(tree.isOnMainVariation(index: d4Index))
       XCTAssertFalse(tree.isOnMainVariation(index: nf3Index))
   }

   func testBranchDeletion() {
       var tree = MoveTree()

       let startPosition = Position.standard
       let afterE4 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!
       let afterE5 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2")!

       let e4 = Move(metaMove: .init(san: "e4", position: startPosition)!)
       let e4Index = tree.add(move: e4, toParentIndex: MoveTree.minimumIndex)

       let e5 = Move(metaMove: .init(san: "e5", position: afterE4)!)
       let e5Index = tree.add(move: e5, toParentIndex: e4Index)

       let nf3 = Move(metaMove: .init(san: "Nf3", position: afterE5)!)
       let _ = tree.add(move: nf3, toParentIndex: e5Index)

       let d4 = Move(metaMove: .init(san: "d4", position: afterE5)!)
       let d4Index = tree.add(move: d4, toParentIndex: e5Index)

       XCTAssertEqual(tree.allCount, 4) // e4, e5, nf3, d4

       // Test deletion
       let deleteSuccess = tree.delete(at: d4Index)
       XCTAssertTrue(deleteSuccess)

       // After deletion, d4 should not exist
       XCTAssertNil(tree.getNodeMove(index: d4Index))
       XCTAssertEqual(tree.allCount, 3) // e4, e5, nf3

       // Check that variations are updated
       let variations = tree.variations(from: e5Index)
       XCTAssertEqual(variations.count, 0) // No more variations
   }

   func testPGNWithBranches() {
       let pgnWithVariations = """
       [Event "Test"]
       [Site "Test"]
       [Date "2025.06.21"]
       [Round "1"]
       [White "Player 1"]
       [Black "Player 2"]
       [Result "*"]

       1. e4 e5 2. Nf3 (2. d4 exd4 3. c3) 2... Nc6 *
       """

       let game = Game(pgn: pgnWithVariations)
       XCTAssertNotNil(game)

       // Check that the game has the expected number of moves
       XCTAssertGreaterThan(game!.moves.allCount, 5)

       // Test that we can regenerate PGN
       let regeneratedPGN = game!.pgn
       XCTAssertTrue(regeneratedPGN.contains("e4 e5"))
       XCTAssertTrue(regeneratedPGN.contains("Nf3"))
       XCTAssertTrue(regeneratedPGN.contains("(2. d4 exd4 3. c3)"))
       XCTAssertTrue(regeneratedPGN.contains("Nc6"))
   }
}
