import Foundation
@testable import ChessKit

/// Test-specific extensions for MoveTree to help with testing
extension MoveTree {
    
    /// Returns indices in the order they were added to the tree
    /// This is useful for tests that expect predictable index ordering
    var orderedIndices: [MoveIndex] {
        var indices: [MoveIndex] = []
        collectIndicesInOrder(from: headNode, into: &indices)
        return Array(indices.dropFirst()) // Remove head node index
    }
    
    /// Gets the nth index in the order moves were added (0-based)
    /// Used for tests that expect specific index values
    func nthIndex(_ n: Int) -> MoveIndex? {
        let ordered = orderedIndices
        return n < ordered.count ? ordered[n] : nil
    }
    
    /// Private helper to collect indices in the order they appear in the tree
    private func collectIndicesInOrder(from node: Node, into indices: inout [MoveIndex]) {
        indices.append(node.index)
        
        // First add the next node (main line)
        if let next = node.next {
            collectIndicesInOrder(from: next, into: &indices)
        }
        
        // Then add all children (variations)
        for child in node.children {
            collectIndicesInOrder(from: child, into: &indices)
        }
    }
}