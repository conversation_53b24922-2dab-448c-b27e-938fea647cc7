//
//  MoveTreeTests.swift
//  ChessKitTests
//

@testable import ChessKit
import ChessKit
import XCTest

final class MoveTreeTests: XCTestCase {

   func testEmptyTree() {
       let moveTree = MoveTree()
       XCTAssertTrue(moveTree.isEmpty)
       XCTAssertNil(moveTree.firstIndex)
       XCTAssertEqual(moveTree.count, 0)
       XCTAssertEqual(moveTree.allCount, 0)
   }

   func testAddAndGetMove() {
       var moveTree = MoveTree()
       // The default init of MoveTree creates a head node with black's color and move number 0.
       // So the first move should be by white and be move number 1.
       XCTAssertEqual(moveTree.initialSideToMove, .white)
       XCTAssertEqual(moveTree.initialNumber, 1)
       
       let metaMove = MetaMove(san: "e4", position: .standard)!
       let move = Move(metaMove: metaMove)

       // Add move to head
       let index1 = moveTree.add(move: move)
       XCTAssertEqual(index1, moveTree.nthIndex(0))
       XCTAssertEqual(moveTree.getNodeMove(index: index1), move)
       XCTAssertEqual(moveTree.count, 1)
       XCTAssertEqual(moveTree.allCount, 1)
       XCTAssertEqual(moveTree.getNodeColor(index: index1), .white)
       XCTAssertEqual(moveTree.getNodeNumber(index: index1), 1)

       let p = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1")!
       let metaMove2 = MetaMove(san: "e5", position: p)!
       let move2 = Move(metaMove: metaMove2)

       // Add second move
       let index2 = moveTree.add(move: move2, toParentIndex: index1)
       XCTAssertEqual(index2, moveTree.nthIndex(1))
       XCTAssertEqual(moveTree.getNodeMove(index: index2), move2)
       XCTAssertEqual(moveTree.count, 2)
       XCTAssertEqual(moveTree.allCount, 2)
       XCTAssertEqual(moveTree.getNodeColor(index: index2), .black)
       XCTAssertEqual(moveTree.getNodeNumber(index: index2), 1)

       // Check navigation
       XCTAssertEqual(moveTree.firstIndex, index1)
       XCTAssertEqual(moveTree.nextIndex(currentIndex: MoveTree.minimumIndex), index1)
       XCTAssertEqual(moveTree.nextIndex(currentIndex: index1), index2)
       XCTAssertNil(moveTree.nextIndex(currentIndex: index2))

       XCTAssertEqual(moveTree.previousIndex(currentIndex: index2), index1)
       XCTAssertEqual(moveTree.previousIndex(currentIndex: index1), MoveTree.minimumIndex)
       XCTAssertNil(moveTree.previousIndex(currentIndex: MoveTree.minimumIndex))
   }

   func testMoveWithComment() {
       var moveTree = MoveTree()
       let metaMove = MetaMove(san: "e4", position: .standard)!
       let comment = "A great opening move"
       let move = Move(metaMove: metaMove, positionComment: Move.PositionComment(text: comment))

       let index = moveTree.add(move: move)

       let retrievedMove = moveTree.getNodeMove(index: index)
       XCTAssertEqual(retrievedMove?.positionComment.text, comment)
   }

   func testHistoryAndFuture() {
       var game = Game()
       let moves = ["e4", "e5", "Nf3", "Nc6"]
       var lastIndex: MoveTree.MoveIndex = game.startingIndex
       var indices: [MoveTree.MoveIndex] = []
       for san in moves {
           lastIndex = game.make(move: san, from: lastIndex)
           indices.append(lastIndex)
       }
       let history = game.moves.history(for: indices[2])
       let expectedHistory = [game.startingIndex, indices[0], indices[1], indices[2]]
       XCTAssertEqual(history, expectedHistory)
       let future = game.moves.future(for: indices[1])
       let expectedFuture = [indices[2], indices[3]]
       XCTAssertEqual(future, expectedFuture)
       let fullVariation = game.moves.fullVariation(for: indices[1])
       let expectedFullVariation = [game.startingIndex, indices[0], indices[1], indices[2], indices[3]]
       XCTAssertEqual(fullVariation, expectedFullVariation)
   }
   
   // MARK: - Overwrite Tests
   
   func testOverwriteBasicFunctionality() {
       // Test MoveTree.overwrite(move:toParentIndex:) basic functionality
       var moveTree = MoveTree()
       
       // Add initial moves: e4, e5, Nf3
       let metaMove1 = MetaMove(san: "e4", position: .standard)!
       let move1 = Move(metaMove: metaMove1)
       let index1 = moveTree.add(move: move1)
       
       let p1 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1")!
       let metaMove2 = MetaMove(san: "e5", position: p1)!
       let move2 = Move(metaMove: metaMove2)
       let index2 = moveTree.add(move: move2, toParentIndex: index1)
       
       let p2 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")!
       let metaMove3 = MetaMove(san: "Nf3", position: p2)!
       let move3 = Move(metaMove: metaMove3)
       let index3 = moveTree.add(move: move3, toParentIndex: index2)
       
       XCTAssertEqual(moveTree.count, 3)
       
       // Overwrite from e5 position with d3 (should delete Nf3 and add d3)
       let metaMoveNew = MetaMove(san: "d3", position: p2)!
       let moveNew = Move(metaMove: metaMoveNew)
       let newIndex = moveTree.overwrite(move: moveNew, toParentIndex: index2)
       
       XCTAssertNotEqual(newIndex, index3) // Should get new index
       XCTAssertEqual(moveTree.count, 3) // Same count but different tree structure
       
       // Verify new move was added correctly
       let addedMove = moveTree.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "d3")
       
       // Verify old Nf3 move was deleted
       XCTAssertNil(moveTree.getNodeMove(index: index3))
       
       // Verify tree navigation works correctly
       XCTAssertEqual(moveTree.nextIndex(currentIndex: index2), newIndex)
       XCTAssertNil(moveTree.nextIndex(currentIndex: newIndex))
   }
   
   func testOverwriteWithVariations() {
       // Test overwrite when there are variations
       var moveTree = MoveTree()
       
       // Add e4, e5
       let metaMove1 = MetaMove(san: "e4", position: .standard)!
       let move1 = Move(metaMove: metaMove1)
       let index1 = moveTree.add(move: move1)
       
       let p1 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1")!
       let metaMove2 = MetaMove(san: "e5", position: p1)!
       let move2 = Move(metaMove: metaMove2)
       let index2 = moveTree.add(move: move2, toParentIndex: index1)
       
       let p2 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")!
       
       // Add main move: Nf3
       let metaMove3 = MetaMove(san: "Nf3", position: p2)!
       let move3 = Move(metaMove: metaMove3)
       let index3 = moveTree.add(move: move3, toParentIndex: index2)
       
       // Add variations: Bc4, d3
       let metaMoveBc4 = MetaMove(san: "Bc4", position: p2)!
       let moveBc4 = Move(metaMove: metaMoveBc4)
       let indexBc4 = moveTree.add(move: moveBc4, toParentIndex: index2)
       
       let metaMoved3 = MetaMove(san: "d3", position: p2)!
       let moved3 = Move(metaMove: metaMoved3)
       let indexd3 = moveTree.add(move: moved3, toParentIndex: index2)
       
       let initialCount = moveTree.allCount
       XCTAssertEqual(initialCount, 5) // e4, e5, Nf3, Bc4, d3
       
       // Overwrite from e5 with Bb5 (should delete all three moves after e5)
       let metaMoveNew = MetaMove(san: "Bb5", position: p2)!
       let moveNew = Move(metaMove: metaMoveNew)
       let newIndex = moveTree.overwrite(move: moveNew, toParentIndex: index2)
       
       XCTAssertEqual(moveTree.count, 3) // e4, e5, Bb5
       
       // Verify new move was added correctly
       let addedMove = moveTree.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "Bb5")
       
       // Verify all old moves were deleted
       XCTAssertNil(moveTree.getNodeMove(index: index3))
       XCTAssertNil(moveTree.getNodeMove(index: indexBc4))
       XCTAssertNil(moveTree.getNodeMove(index: indexd3))
       
       // Verify no variations exist after e5
       XCTAssertEqual(moveTree.variations(from: index2).count, 0)
   }
   
   func testOverwriteWithNoMovesToDelete() {
       // Test overwrite when there are no moves to delete
       var moveTree = MoveTree()
       
       // Add only e4, e5
       let metaMove1 = MetaMove(san: "e4", position: .standard)!
       let move1 = Move(metaMove: metaMove1)
       let index1 = moveTree.add(move: move1)
       
       let p1 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1")!
       let metaMove2 = MetaMove(san: "e5", position: p1)!
       let move2 = Move(metaMove: metaMove2)
       let index2 = moveTree.add(move: move2, toParentIndex: index1)
       
       XCTAssertEqual(moveTree.count, 2)
       
       // Overwrite from e5 with Nf3 (no moves to delete, just add)
       let p2 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")!
       let metaMoveNew = MetaMove(san: "Nf3", position: p2)!
       let moveNew = Move(metaMove: metaMoveNew)
       let newIndex = moveTree.overwrite(move: moveNew, toParentIndex: index2)
       
       XCTAssertEqual(moveTree.count, 2)
       
       // Verify new move was not added
       let addedMove = moveTree.getNodeMove(index: newIndex)
       XCTAssertEqual(addedMove?.metaMove?.san, "e5")
       
       // Verify tree navigation works
       XCTAssertNotEqual(moveTree.nextIndex(currentIndex: index2), newIndex)
   }
   
   func testOverwriteFailureCase() {
       // Test overwrite when deleteAllAfter fails
       var moveTree = MoveTree()
       
       // Add e4, e5
       let metaMove1 = MetaMove(san: "e4", position: .standard)!
       let move1 = Move(metaMove: metaMove1)
       let index1 = moveTree.add(move: move1)
       
       let p1 = Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1")!
       let metaMove2 = MetaMove(san: "e5", position: p1)!
       let move2 = Move(metaMove: metaMove2)
       let _ = moveTree.add(move: move2, toParentIndex: index1)
       
       // Try to overwrite with invalid parent index
       let p2 = Position(fen: "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")!
       let metaMoveNew = MetaMove(san: "Nf3", position: p2)!
       let moveNew = Move(metaMove: metaMoveNew)
       let resultIndex = moveTree.overwrite(move: moveNew, toParentIndex: 999)
       
       XCTAssertEqual(resultIndex, 999) // Should return original invalid index
       XCTAssertEqual(moveTree.count, 2) // No changes should occur
   }
}
