//
//  VariationDetectionTests.swift
//  ChessKitTests
//
//  Created on 2025/6/8.
//

@testable import ChessKit
import XCTest

final class VariationDetectionTests: XCTestCase {

   func testVariationsFromIndex() {
       // Create a game with variations
       var game = Game()
       
       // Main line: 1. e4 e5 2. Nf3
       game.make(moves: ["e4", "e5", "Nf3"], from: game.startingIndex)
       
       let e5Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5" }!
       
       // Add variations at move 2 for White: 2. d4 and 2. Bc4
       game.make(move: "d4", from: e5Index)
       game.make(move: "Bc4", from: e5Index)
       
       // Test that we can detect variations from e5 position
       let variations = game.moves.variations(from: e5Index)
       
       // Should have 2 variations (d4 and Bc4)
       XCTAssertEqual(variations.count, 2)
       
       // Verify the moves are correct
       let variationSans = variations.compactMap { game.moves.getNodeMove(index: $0)?.metaMove?.san }
       XCTAssertTrue(variationSans.contains("d4"))
       XCTAssertTrue(variationSans.contains("Bc4"))
   }
   
   func testNoVariationsFromIndex() {
       // Create a simple game without variations
       var game = Game()
       
       game.make(moves: ["e4", "e5"], from: game.startingIndex)
       
       let e4Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "e4" }!
       let e5Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5" }!
       
       // Test that no variations exist from e4 position
       let variations = game.moves.variations(from: e4Index)
       XCTAssertEqual(variations.count, 0)
       
       // Test that no variations exist from e5 position
       let variationsFromE5 = game.moves.variations(from: e5Index)
       XCTAssertEqual(variationsFromE5.count, 0)
   }
   
   func testVariationPGNGeneration() {
       // Create a game with a variation
       var game = Game()
       
       game.make(moves: ["e4", "e5", "Nf3"], from: game.startingIndex)
       
       let e5Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5" }!
       
       // Add a variation: 2. d4 exd4 3. c3
       let d4Index = game.make(move: "d4", from: e5Index)
       let ed4Index = game.make(move: "exd4", from: d4Index)
       game.make(move: "c3", from: ed4Index)
       
       // Test PGN generation for the variation
       let pgn = game.pgn
       
       XCTAssertTrue(pgn.contains("(2. d4 exd4 3. c3)"))
   }
   
   func testComplexVariationStructure() {
       // Test a more complex variation structure
       var game = Game()
       
       // Main line: 1. e4 e5 2. Nf3 Nc6
       game.make(moves: ["e4", "e5", "Nf3", "Nc6"], from: game.startingIndex)
       
       let e5Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5" }!
       let nf3Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "Nf3" }!

       // Add variations at move 2 for White
       game.make(move: "d4", from: e5Index)
       game.make(move: "Bc4", from: e5Index)
       
       // Add variations at move 2 for Black (after Nf3)
       game.make(move: "d6", from: nf3Index)
       game.make(move: "f5", from: nf3Index)
       
       // Test variations from e5 (White's move 2)
       let whiteVariations = game.moves.variations(from: e5Index)
       XCTAssertEqual(whiteVariations.count, 2) // d4 and Bc4
       
       // Test variations from nf3 (Black's move 2)
       let blackVariations = game.moves.variations(from: nf3Index)
       XCTAssertEqual(blackVariations.count, 2) // d6 and f5
   }
   
   func testMainLineWithVariations() {
       // Test scenario where both main line and variations exist
       var game = Game()
       
       game.make(moves: ["e4", "e5", "Nf3"], from: game.startingIndex)
       
       let e5Index = game.moves.indices.first { game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5" }!
       
       // Add variations: 2. d4 and 2. Bc4
       game.make(move: "d4", from: e5Index)
       game.make(move: "Bc4", from: e5Index)
       
       // From e5 position, we should have:
       // - Main line move available (Nf3 at e5.next)
       // - Variations available (d4 and Bc4)
       
       let nextMainLineIndex = game.moves.nextIndex(currentIndex: e5Index)
       XCTAssertNotNil(nextMainLineIndex)
       XCTAssertEqual(game.moves.getNodeMove(index: nextMainLineIndex!)?.metaMove?.san, "Nf3")
       
       let variations = game.moves.variations(from: e5Index)
       XCTAssertEqual(variations.count, 2) // Two variations exist
   }
}
