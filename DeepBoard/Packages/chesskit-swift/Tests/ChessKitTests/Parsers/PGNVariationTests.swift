//
//  PGNVariationTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class PGNVariationTests: XCTestCase {

  func testSimpleVariation() {
    let pgn = """
    [Event "Test"]
    [Site "Test"]
    [Date "2025.06.02"]
    [Round "1"]
    [White "Player 1"]
    [Black "Player 2"]
    [Result "*"]

    1. e4 e5 2. Nf3 (2. d4 exd4 3. c3) Nc6 *
    """

    let game = PGNParser.parse(game: pgn)
    XCTAssertNotNil(game)

    // Check that main line exists
    // Get the move indices from the tree
    let indices = game?.moves.indices.sorted() ?? []
    XCTAssertGreaterThanOrEqual(indices.count, 4)
    
    // Check main line moves by accessing them through the tree
    let mainLineIndices = indices.filter { game?.moves.isOnMainVariation(index: $0) == true }
    XCTAssertGreaterThanOrEqual(mainLineIndices.count, 4)
    
    // Verify the moves exist in the tree
    for index in mainLineIndices {
      XCTAssertNotNil(game?.moves.getNodeMove(index: index))
    }
  }
  
  func testNestedVariation() {
    let pgn = """
    [Event "Test"]
    [Site "Test"]
    [Date "2025.06.02"]
    [Round "1"]
    [White "Player 1"]
    [Black "Player 2"]
    [Result "*"]
    
    1. e4 e5 2. Nf3 Nc6 3. Bb5 (3. Bc4 Bc5 (3... f5 4. exf5 Nf6) 4. d3 f5) 3... a6 *
    """
    
    let game = PGNParser.parse(game: pgn)
    XCTAssertNotNil(game)
    
    // Check that the game has moves
    let indices = game?.moves.indices.sorted() ?? []
    XCTAssertGreaterThan(indices.count, 0)
    
    // Verify that variations exist
    XCTAssertTrue(game?.moves.hasVariation == true)
  }
  
  func testBlackVariation() {
    let pgn = """
    [Event "Test"]
    [Site "Test"]
    [Date "2025.06.02"]
    [Round "1"]
    [White "Player 1"]
    [Black "Player 2"]
    [Result "*"]
    
    1. e4 c5 2. Nf3 d6 (2... e6 3. d4) 3. d4 cxd4 *
    """
    
    let game = PGNParser.parse(game: pgn)
    XCTAssertNotNil(game)
    
    // Check that the game has moves
    let indices = game?.moves.indices.sorted() ?? []
    XCTAssertGreaterThan(indices.count, 0)
    
    // Verify that variations exist
    XCTAssertTrue(game?.moves.hasVariation == true)
  }
  
  func testPGNRoundTrip() {
    let originalPgn = """
    [Event "Test"]
    [Site "Test"]
    [Date "2025.06.02"]
    [Round "1"]
    [White "Player 1"]
    [Black "Player 2"]
    [Result "*"]

    1. e4 e5 2. Nf3 (2. d4 exd4 3. c3) Nc6 3. Bb5 a6 *
    """

    let game = PGNParser.parse(game: originalPgn)
    XCTAssertNotNil(game)

    let regeneratedPgn = PGNParser.convert(game: game!)

    // Parse the regenerated PGN to ensure it's valid
    let reparsedGame = PGNParser.parse(game: regeneratedPgn)
    XCTAssertNotNil(reparsedGame)

    // Check that both games have the same moves
    XCTAssertEqual(game?.moves.indices.count, reparsedGame?.moves.indices.count)
  }

  func testUserReportedIssues() {
    // Test case 1: User's first example
    let pgn1 = "1. e4 e5 2. Nf3 (2. d4 exd4 3. c3) Nc6"
    let game1 = PGNParser.parse(game: pgn1)
    XCTAssertNotNil(game1)

    // Should have moves
    let indices1 = game1?.moves.indices.sorted() ?? []
    XCTAssertGreaterThan(indices1.count, 0)
    
    // Should have variations
    XCTAssertTrue(game1?.moves.hasVariation == true)

    // Test case 2: User's second example
    let pgn2 = "1. e4 c5 2. Nf3 d6 (2... e6 3. d4) 3. d4 cxd4"
    let game2 = PGNParser.parse(game: pgn2)
    XCTAssertNotNil(game2)

    // Should have moves
    let indices2 = game2?.moves.indices.sorted() ?? []
    XCTAssertGreaterThan(indices2.count, 0)
    
    // Should have variations
    XCTAssertTrue(game2?.moves.hasVariation == true)
  }

}
