//
//  PGNParserTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class PGNParserTests: XCTestCase {

  func testGameFromPGN() {
    let game = PGNParser.parse(game: Game.fischerSpassky)
    let gameFromPGN = Game(pgn: Game.fischerSpassky)

    XCTAssertEqual(game, gameFromPGN)
  }

  func testTagParsing() {
    let game = PGNParser.parse(game: Game.fischerSpassky)

    // tags
    XCTAssertEqual(game?.tags.event, "F/S Return Match")
    XCTAssertEqual(game?.tags.site, "Belgrade, Serbia JUG")
    XCTAssertEqual(game?.tags.date, "1992.11.04")
    XCTAssertEqual(game?.tags.round, "29")
    XCTAssertEqual(game?.tags.white, "<PERSON>, <PERSON>.")
    XCTAssertEqual(game?.tags.black, "<PERSON><PERSON><PERSON>, <PERSON>")
    XCTAssertEqual(game?.tags.result, "1/2-1/2")
  }
  
  func testPGNOutputIncludesResult() {
    // Test with different result types
    let testCases = [
      ("1-0", "<PERSON> wins"),
      ("0-1", "<PERSON> wins"), 
      ("1/2-1/2", "Draw"),
      ("*", "Ongoing game")
    ]
    
    for (result, description) in testCases {
      // Create a simple game with the specified result
      let pgnString = """
        [Event "Test Game"]
        [Site "Test Location"]
        [Date "2023.01.01"]
        [Round "1"]
        [White "Player A"]
        [Black "Player B"]
        [Result "\(result)"]
        
        1. e4 e5 2. Nf3 Nc6 3. Bb5 \(result)
        """
      
      guard let game = PGNParser.parse(game: pgnString) else {
        XCTFail("Failed to parse PGN for result: \(result)")
        continue
      }
      
      // Convert back to PGN
      let convertedPGN = PGNParser.convert(game: game)
      
      // Check that the result appears at the end of the movetext
      XCTAssertTrue(convertedPGN.hasSuffix(result), 
                   "PGN output should end with result '\(result)' for \(description). Got: '\(convertedPGN)'")
      
      // Ensure result appears in tags section too
      XCTAssertTrue(convertedPGN.contains(#"[Result "\#(result)"]"#),
                   "PGN should contain result tag for \(description)")
    }
  }
  
  func testPGNOutputWithEmptyResult() {
    // Test game with empty result - should default to * (ongoing)
    let pgnString = """
      [Event "Test Game"]
      [White "Player A"]  
      [Black "Player B"]
      
      1. e4 e5 2. Nf3 Nc6
      """
    
    guard let game = PGNParser.parse(game: pgnString) else {
      XCTFail("Failed to parse PGN with empty result")
      return
    }
    
    let convertedPGN = PGNParser.convert(game: game)
    
    // Should end with * indicating ongoing game
    XCTAssertTrue(convertedPGN.hasSuffix("*"), "Should end with * for ongoing game")
  }
  
  func testIntelligentResultDetermination() {
    // Test checkmate detection
    let checkmateTest = """
      [Event "Checkmate Test"]
      [White "Player A"]
      [Black "Player B"]
      
      1. f3 e5 2. g4 Qh4#
      """
    
    guard let checkmateGame = PGNParser.parse(game: checkmateTest) else {
      XCTFail("Failed to parse checkmate PGN")
      return
    }
    
    let checkmateOutput = PGNParser.convert(game: checkmateGame)
    XCTAssertTrue(checkmateOutput.hasSuffix("0-1"), "Should detect black checkmate win: \(checkmateOutput.suffix(10))")
    
    // Note: Stalemate detection would require more complex position analysis 
    // which is beyond the scope of this basic implementation
    
    // For ongoing games without explicit result, should default to *
    let ongoingTest = """
      [Event "Ongoing Test"]
      [White "Player A"]
      [Black "Player B"]
      
      1. e4 e5 2. Nf3 Nc6 3. Bb5
      """
    
    guard let ongoingGame = PGNParser.parse(game: ongoingTest) else {
      XCTFail("Failed to parse ongoing PGN")
      return
    }
    
    let ongoingOutput = PGNParser.convert(game: ongoingGame)
    XCTAssertTrue(ongoingOutput.hasSuffix("*"), "Should default to * for ongoing game: \(ongoingOutput.suffix(10))")
  }
  
  func testExistingResultPreserved() {
    // Test that existing valid result tags are preserved
    let existingResultTest = """
      [Event "Result Tag Test"]
      [White "Player A"]
      [Black "Player B"]
      [Result "1-0"]
      
      1. e4 e5 2. Nf3 Nc6
      """
    
    guard let game = PGNParser.parse(game: existingResultTest) else {
      XCTFail("Failed to parse PGN with existing result")
      return
    }
    
    let output = PGNParser.convert(game: game)
    XCTAssertTrue(output.hasSuffix("1-0"), "Should preserve existing result tag: \(output.suffix(10))")
  }

  func testCustomTagParsing() {
    // invalid pair
    let g1 = PGNParser.parse(game: "[a] 1. e4 e5")
    XCTAssertNil(g1?.tags.other["a"])

    // custom tag
    let g2 = PGNParser.parse(game: "[CustomTag \"Value\"] 1. e4 e5")
    XCTAssertEqual(g2?.tags.other["CustomTag"], "Value")

    // duplicate tags
    let g3 = PGNParser.parse(game: "[CustomTag \"Value\"] [CustomTag \"Value2\"] 1. e4 e5")
    XCTAssertEqual(g3?.tags.other["CustomTag"], "Value")
  }

  func testMoveTextParsing() {
    let game = PGNParser.parse(game: Game.fischerSpassky)

    // starting position + 85 ply
    XCTAssertEqual(game?.positions.keys.count, 86)

    // Get main variation indices for testing
    let move0Index = game?.moves.mainVariationIndex(at: 0)
    let move1Index = game?.moves.mainVariationIndex(at: 1)
    let move5Index = game?.moves.mainVariationIndex(at: 5)
    let move6Index = game?.moves.mainVariationIndex(at: 6)
    let move18Index = game?.moves.mainVariationIndex(at: 18)
    let move35Index = game?.moves.mainVariationIndex(at: 35)
    let move34Index = game?.moves.mainVariationIndex(at: 34)
    let move70Index = game?.moves.mainVariationIndex(at: 70)
    
    XCTAssertEqual(
      move0Index != nil ? game?.moves.getNodeMove(index: move0Index!)?.metaMove?.moveAssessment : nil, .blunder)
    XCTAssertEqual(
      move1Index != nil ? game?.moves.getNodeMove(index: move1Index!)?.metaMove?.moveAssessment : nil, .brilliant)
    XCTAssertEqual(
      move5Index != nil ? game?.moves.getNodeMove(index: move5Index!)?.positionComment.text : nil, "This opening is called the Ruy Lopez.")
    XCTAssertEqual(
      move6Index != nil ? game?.moves.getNodeMove(index: move6Index!)?.positionComment.text : nil, "test comment")
    XCTAssertEqual(
      move18Index != nil ? game?.moves.getNodeMove(index: move18Index!)?.metaMove?.end : nil, .d4)
    XCTAssertEqual(
      move35Index != nil ? game?.moves.getNodeMove(index: move35Index!)?.metaMove?.piece.kind : nil, .queen)
    XCTAssertEqual(
      move34Index != nil ? game?.moves.getNodeMove(index: move34Index!)?.metaMove?.end : nil, .e7)
    XCTAssertEqual(
      move70Index != nil ? game?.moves.getNodeMove(index: move70Index!)?.metaMove?.checkState : nil, .check)
  }

  func testInitialPositionCommentsParsing() {
    // Test PGN with only initial position comments (no moves)
    let pgnWithOnlyComments = "{ [%csl Ge4] }"
    let game = PGNParser.parse(game: pgnWithOnlyComments)
    
    XCTAssertNotNil(game)
    XCTAssertTrue(game!.moves.isEmpty) // No actual moves
    
    // Check that head node has the visual annotation
    let headNodeMove = game!.moves.getNodeMove(index: MoveTree.minimumIndex)
    XCTAssertNotNil(headNodeMove)
    XCTAssertNil(headNodeMove!.metaMove) // No actual move
    XCTAssertEqual(headNodeMove!.positionComment.visualAnnotations.squareHighlights.count, 1)
    XCTAssertEqual(headNodeMove!.positionComment.visualAnnotations.squareHighlights[0].square, .e4)
    XCTAssertEqual(headNodeMove!.positionComment.visualAnnotations.squareHighlights[0].color, .green)
    
    // Test with text comment and visual annotation
    let pgnWithTextAndVisualComments = "{ This is a starting position with e4 highlighted [%csl Ge4] }"
    let game2 = PGNParser.parse(game: pgnWithTextAndVisualComments)
    
    XCTAssertNotNil(game2)
    let headNodeMove2 = game2!.moves.getNodeMove(index: MoveTree.minimumIndex)
    XCTAssertNotNil(headNodeMove2)
    XCTAssertTrue(headNodeMove2!.positionComment.text.contains("This is a starting position"))
    XCTAssertEqual(headNodeMove2!.positionComment.visualAnnotations.squareHighlights.count, 1)
    
    // Test with arrows
    let pgnWithArrows = "{ [%cal Ge2e4,Re1g1] }"
    let game3 = PGNParser.parse(game: pgnWithArrows)
    
    XCTAssertNotNil(game3)
    let headNodeMove3 = game3!.moves.getNodeMove(index: MoveTree.minimumIndex)
    XCTAssertNotNil(headNodeMove3)
    XCTAssertEqual(headNodeMove3!.positionComment.visualAnnotations.arrows.count, 2)
    
    // Find green and red arrows
    let greenArrow = headNodeMove3!.positionComment.visualAnnotations.arrows.first { $0.color == .green }
    let redArrow = headNodeMove3!.positionComment.visualAnnotations.arrows.first { $0.color == .red }
    
    XCTAssertNotNil(greenArrow)
    XCTAssertNotNil(redArrow)
    XCTAssertEqual(greenArrow!.from, .e2)
    XCTAssertEqual(greenArrow!.to, .e4)
    XCTAssertEqual(redArrow!.from, .e1)
    XCTAssertEqual(redArrow!.to, .g1)
  }

}
