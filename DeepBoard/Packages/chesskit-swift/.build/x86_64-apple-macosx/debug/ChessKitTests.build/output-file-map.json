{"": {"swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swiftdeps"}}