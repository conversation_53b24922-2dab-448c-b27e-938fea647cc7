/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift
