{"": {"swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swiftdeps"}}