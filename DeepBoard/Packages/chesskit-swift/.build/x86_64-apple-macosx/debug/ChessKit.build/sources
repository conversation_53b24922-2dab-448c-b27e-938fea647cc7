/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift
'/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift'
'/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift'
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift
/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift
