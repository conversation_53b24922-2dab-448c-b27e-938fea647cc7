{"builtTestProducts": [{"binaryPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.xctest/Contents/MacOS/ChessKitPackageTests", "packagePath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift", "productName": "ChessKitPackageTests"}], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.ChessKit-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"}], "isLibrary": true, "moduleName": "ChessKit", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx10.15", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/ChessKit-Swift.h", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build", "wholeModuleOptimization": false}, "C.ChessKitPackageTests-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources"}], "isLibrary": true, "moduleName": "ChessKitPackageTests", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitPackageTests.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx10.15", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/ChessKitPackageTests-Swift.h", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitPackageTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build", "wholeModuleOptimization": false}, "C.ChessKitTests-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources"}], "isLibrary": true, "moduleName": "ChessKitTests", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"ChessKit": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ChessKit", "-package-name", "chesskit_swift", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift", "-I", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx10.15", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/ChessKit-Swift.h", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "ChessKitPackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ChessKitPackageTests", "-package-name", "chesskit_swift", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift", "-I", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx10.15", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/ChessKitPackageTests-Swift.h", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "ChessKitTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ChessKitTests", "-package-name", "chesskit_swift", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift", "-I", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"ChessKit": [], "ChessKitPackageTests": ["ChessKitTests", "ChessKit"], "ChessKitTests": ["ChessKit"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {"/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift": {"inputs": [], "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift"}]}}, "writeCommands": {"/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}