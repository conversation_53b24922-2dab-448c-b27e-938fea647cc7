---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.2/System.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1733472232000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.2/System.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            412156
  - mtime:           1731214587000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            2034701
    sdk_relative:    true
  - mtime:           1731215502000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1731215914000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3749
    sdk_relative:    true
  - mtime:           1731215935000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            998
    sdk_relative:    true
  - mtime:           1731215945000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1034
    sdk_relative:    true
  - mtime:           1731215950000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1035
    sdk_relative:    true
  - mtime:           1731215937000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1460
    sdk_relative:    true
  - mtime:           1731215958000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            730
    sdk_relative:    true
  - mtime:           1731215915000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            24003
    sdk_relative:    true
  - mtime:           1731214847000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5847
    sdk_relative:    true
  - mtime:           1731215979000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            19812
    sdk_relative:    true
  - mtime:           1731216731000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            248725
    sdk_relative:    true
  - mtime:           1731216802000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22702
    sdk_relative:    true
  - mtime:           1731217484000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            94401
    sdk_relative:    true
version:         1
...
