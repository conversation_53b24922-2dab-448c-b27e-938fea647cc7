{"": {"swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.d", "object": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swiftdeps"}}