/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o
