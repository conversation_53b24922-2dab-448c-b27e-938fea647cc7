client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "ChessKit-x86_64-apple-macosx15.0-debug.module": ["<ChessKit-x86_64-apple-macosx15.0-debug.module>"]
  "ChessKitPackageTests-x86_64-apple-macosx15.0-debug.module": ["<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.module>"]
  "ChessKitPackageTests-x86_64-apple-macosx15.0-debug.test": ["<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.test>"]
  "ChessKitTests-x86_64-apple-macosx15.0-debug.module": ["<ChessKitTests-x86_64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<ChessKit-x86_64-apple-macosx15.0-debug.module>"]
  "test": ["<ChessKit-x86_64-apple-macosx15.0-debug.module>","<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.test>","<ChessKitTests-x86_64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"

  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources"

  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift":
    tool: test-entry-point-tool
    inputs: []
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift"]

  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList"

  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources"

  "/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<ChessKit-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"]
    outputs: ["<ChessKit-x86_64-apple-macosx15.0-debug.module>"]

  "<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitPackageTests.swiftmodule"]
    outputs: ["<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.module>"]

  "<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.test>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.xctest/Contents/MacOS/ChessKitPackageTests"]
    outputs: ["<ChessKitPackageTests-x86_64-apple-macosx15.0-debug.test>"]

  "<ChessKitTests-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule"]
    outputs: ["<ChessKitTests-x86_64-apple-macosx15.0-debug.module>"]

  "C.ChessKit-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENValidator.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"]
    description: "Compiling Swift Module 'ChessKit' (25 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ChessKit","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/sources","-I","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx10.15","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/ChessKit-Swift.h","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","chesskit_swift"]

  "C.ChessKitPackageTests-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.derived/runner.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitPackageTests.swiftmodule"]
    description: "Compiling Swift Module 'ChessKitPackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ChessKitPackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitPackageTests.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/sources","-I","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx10.15","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/ChessKitPackageTests-Swift.h","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","chesskit_swift"]

  "C.ChessKitPackageTests-x86_64-apple-macosx15.0-debug.test":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/FENValidator.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKit.build/UndoState.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.xctest/Contents/MacOS/ChessKitPackageTests"]
    description: "Linking ./.build/x86_64-apple-macosx/debug/ChessKitPackageTests.xctest/Contents/MacOS/ChessKitPackageTests"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug","-o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.xctest/Contents/MacOS/ChessKitPackageTests","-module-name","ChessKitPackageTests","-Xlinker","-no_warn_duplicate_libraries","-Xlinker","-bundle","-Xlinker","-rpath","-Xlinker","@loader_path/../../../","@/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitPackageTests.product/Objects.LinkFileList","-target","x86_64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitPackageTests.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.ChessKitTests-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/BoardTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/CastlingTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/FENValidatorTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/GameTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeEditingTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/MoveTree/MoveTreeTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveHashBasedTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/MoveTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SimpleMoveHashTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Moves/SpecialMoveTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PGNTextGenerationTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/EngineLANParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/FENParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNMultiGameParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/PGNVariationTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Parsers/SANParserTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/BoardPerformanceTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Performance/PGNParserPerformanceTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/PieceTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/SquareTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/UndoRedoTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MockBoardDelegate.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/MoveTreeTestExtensions.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SampleGames.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/Utilities/SamplePositions.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/VariationDetectionTests.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/CastlingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENValidatorTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/GameTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeEditingTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveHashBasedTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SimpleMoveHashTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SpecialMoveTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNTextGenerationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/EngineLANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/FENParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNMultiGameParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNVariationTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SANParserTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/BoardPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PGNParserPerformanceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/PieceTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SquareTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/UndoRedoTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MockBoardDelegate.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/MoveTreeTestExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SampleGames.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/SamplePositions.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/VariationDetectionTests.swift.o","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule"]
    description: "Compiling Swift Module 'ChessKitTests' (27 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ChessKitTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules/ChessKitTests.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ChessKitTests.build/sources","-I","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/chesskit-swift/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","chesskit_swift"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests/ChessKitTests/","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Package.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

