//
//  FENValidator.swift
//  ChessKit
//
//  Validates Forsyth–Edwards Notation (FEN) strings for chess positions.
//  Provides comprehensive validation of FEN structure and chess logic.
//

/// Validation result for detailed FEN analysis
public struct FENValidationResult {
    public let isValid: Bool
    public let hasEnPassantWarning: Bool
    public let validEnPassantSquares: [String]
    
    public init(isValid: Bool, hasEnPassantWarning: Bool = false, validEnPassantSquares: [String] = []) {
        self.isValid = isValid
        self.hasEnPassantWarning = hasEnPassantWarning
        self.validEnPassantSquares = validEnPassantSquares
    }
}

/// Validates FEN strings for correctness and chess logic compliance
public struct FENValidator {
    
    /// Validates a complete FEN string
    /// - Parameter components: FEN components split by whitespace
    /// - Returns: true if the FEN is valid, false otherwise
    public static func isValidFEN(_ components: [String]) -> Bool {
        guard components.count == 6 else { return false }
        
        guard isValidPiecePlacement(components[0]) else { return false }
        guard isValidActiveColor(components[1]) else { return false }
        guard isValidCastlingRights(components[2]) else { return false }
        guard isValidEnPassant(components[3]) else { return false }
        guard isValidHalfmove(components[4]) else { return false }
        guard isValidFullmove(components[5]) else { return false }
        
        let pieces = parsePieces(components[0])
        guard isValidChessLogic(pieces: pieces, 
                              activeColor: components[1],
                              castlingRights: components[2],
                              enPassant: components[3]) else { return false }
        
        return true
    }
    
    /// Validates a complete FEN string from a single string
    /// - Parameter fen: Complete FEN string
    /// - Returns: true if the FEN is valid, false otherwise
    public static func isValidFEN(_ fen: String) -> Bool {
        let components = fen.components(separatedBy: .whitespaces)
        return isValidFEN(components)
    }
    
    /// Validates a complete FEN string with detailed results
    /// - Parameter components: FEN components split by whitespace
    /// - Returns: FENValidationResult with detailed validation information
    public static func validateFENDetailed(_ components: [String]) -> FENValidationResult {
        guard components.count == 6 else { 
            return FENValidationResult(isValid: false)
        }
        
        guard isValidPiecePlacement(components[0]) else { 
            return FENValidationResult(isValid: false)
        }
        guard isValidActiveColor(components[1]) else { 
            return FENValidationResult(isValid: false)
        }
        guard isValidCastlingRights(components[2]) else { 
            return FENValidationResult(isValid: false)
        }
        guard isValidHalfmove(components[4]) else { 
            return FENValidationResult(isValid: false)
        }
        guard isValidFullmove(components[5]) else { 
            return FENValidationResult(isValid: false)
        }
        
        // Check en passant format first
        let enPassantFormatValid = isValidEnPassant(components[3])
        
        let pieces = parsePieces(components[0])
        let chessLogicValid = isValidChessLogicExcludingEnPassant(pieces: pieces, 
                                                                 activeColor: components[1],
                                                                 castlingRights: components[2])
        
        guard chessLogicValid else { 
            return FENValidationResult(isValid: false)
        }
        
        // Check en passant logic
        let enPassantLogicValid = isValidEnPassantLogic(pieces: pieces, 
                                                       activeColor: components[1], 
                                                       enPassant: components[3])
        
        // Get valid en passant squares for this position
        let validEnPassantSquares = getValidEnPassantSquares(pieces: pieces, activeColor: components[1])
        
        if !enPassantFormatValid {
            // Invalid format is always an error
            return FENValidationResult(isValid: false)
        } else if !enPassantLogicValid {
            // Invalid logic but valid format is a warning - still valid FEN
            return FENValidationResult(isValid: true, 
                                     hasEnPassantWarning: true, 
                                     validEnPassantSquares: validEnPassantSquares)
        } else {
            // Everything is valid
            return FENValidationResult(isValid: true, 
                                     validEnPassantSquares: validEnPassantSquares)
        }
    }
    
    /// Validates a complete FEN string with detailed results from a single string
    /// - Parameter fen: Complete FEN string
    /// - Returns: FENValidationResult with detailed validation information
    public static func validateFENDetailed(_ fen: String) -> FENValidationResult {
        let components = fen.components(separatedBy: .whitespaces)
        return validateFENDetailed(components)
    }
    
    /// Validates the piece placement field of a FEN string
    /// - Parameter placement: The piece placement field (first field of FEN)
    /// - Returns: true if the piece placement is valid, false otherwise
    public static func isValidPiecePlacement(_ placement: String) -> Bool {
        let ranks = placement.components(separatedBy: "/")
        guard ranks.count == 8 else { return false }
        
        for rank in ranks {
            guard isValidRank(rank) else { return false }
        }
        
        return true
    }
    
    /// Validates a single rank within the piece placement
    /// - Parameter rank: A single rank string (e.g., "rnbqkbnr" or "8")
    /// - Returns: true if the rank is valid, false otherwise
    public static func isValidRank(_ rank: String) -> Bool {
        let validChars = Set("pnbrqkPNBRQK12345678")
        var squareCount = 0
        var prevWasDigit = false
        
        for char in rank {
            guard validChars.contains(char) else { return false }
            
            if char.isNumber {
                if prevWasDigit { return false }
                guard let digit = Int(String(char)), digit >= 1 && digit <= 8 else { return false }
                squareCount += digit
                prevWasDigit = true
            } else {
                squareCount += 1
                prevWasDigit = false
            }
        }
        
        return squareCount == 8
    }
    
    /// Validates the active color field
    /// - Parameter color: The active color field ("w" or "b")
    /// - Returns: true if the active color is valid, false otherwise
    public static func isValidActiveColor(_ color: String) -> Bool {
        return color == "w" || color == "b"
    }
    
    /// Validates the castling rights field
    /// - Parameter rights: The castling rights field (e.g., "KQkq", "Kq", "-")
    /// - Returns: true if the castling rights are valid, false otherwise
    public static func isValidCastlingRights(_ rights: String) -> Bool {
        if rights == "-" { return true }
        
        let validChars = Set("KQkq")
        var seen = Set<Character>()
        
        for char in rights {
            guard validChars.contains(char) else { return false }
            guard !seen.contains(char) else { return false }
            seen.insert(char)
        }
        
        return true
    }
    
    /// Validates the en passant field
    /// - Parameter enPassant: The en passant field (e.g., "e3", "a6", "-")
    /// - Returns: true if the en passant field is valid, false otherwise
    public static func isValidEnPassant(_ enPassant: String) -> Bool {
        if enPassant == "-" { return true }
        
        guard enPassant.count == 2 else { return false }
        
        let chars = Array(enPassant)
        let file = chars[0]
        let rank = chars[1]
        
        guard file >= "a" && file <= "h" else { return false }
        guard rank == "3" || rank == "6" else { return false }
        
        return true
    }
    
    /// Validates the halfmove clock field
    /// - Parameter halfmove: The halfmove clock field (e.g., "0", "50")
    /// - Returns: true if the halfmove clock is valid, false otherwise
    public static func isValidHalfmove(_ halfmove: String) -> Bool {
        guard let value = Int(halfmove) else { return false }
        return value >= 0
    }
    
    /// Validates the fullmove number field
    /// - Parameter fullmove: The fullmove number field (e.g., "1", "25")
    /// - Returns: true if the fullmove number is valid, false otherwise
    public static func isValidFullmove(_ fullmove: String) -> Bool {
        guard let value = Int(fullmove) else { return false }
        return value >= 1
    }
    
    /// Parses pieces from placement string into a dictionary
    /// - Parameter placement: The piece placement field
    /// - Returns: Dictionary mapping piece characters to their positions
    public static func parsePieces(_ placement: String) -> [Character: [(Int, Int)]] {
        var pieces: [Character: [(Int, Int)]] = [:]
        let ranks = placement.components(separatedBy: "/")
        
        for (rankIndex, rank) in ranks.enumerated() {
            var fileIndex = 0
            
            for char in rank {
                if char.isNumber {
                    if let spaces = Int(String(char)) {
                        fileIndex += spaces
                    }
                } else {
                    pieces[char, default: []].append((fileIndex, 7 - rankIndex))
                    fileIndex += 1
                }
            }
        }
        
        return pieces
    }
    
    /// Validates chess-specific logic rules from a complete FEN string
    /// - Parameter fen: Complete FEN string
    /// - Returns: true if chess logic is valid, false otherwise
    public static func isValidChessLogic(_ fen: String) -> Bool {
        let components = fen.components(separatedBy: .whitespaces)
        guard components.count == 6 else { return false }
        
        let pieces = parsePieces(components[0])
        return isValidChessLogic(pieces: pieces, 
                               activeColor: components[1],
                               castlingRights: components[2], 
                               enPassant: components[3])
    }
    
    /// Validates chess-specific logic rules
    /// - Parameters:
    ///   - pieces: Dictionary of piece positions
    ///   - activeColor: Active color ("w" or "b")
    ///   - castlingRights: Castling rights string
    ///   - enPassant: En passant target square
    /// - Returns: true if chess logic is valid, false otherwise
    public static func isValidChessLogic(pieces: [Character: [(Int, Int)]],
                                        activeColor: String,
                                        castlingRights: String,
                                        enPassant: String) -> Bool {
        
        guard isValidChessLogicExcludingEnPassant(pieces: pieces, activeColor: activeColor, castlingRights: castlingRights) else { return false }
        guard isValidEnPassantLogic(pieces: pieces, activeColor: activeColor, enPassant: enPassant) else { return false }
        
        return true
    }
    
    /// Validates chess-specific logic rules excluding en passant
    /// - Parameters:
    ///   - pieces: Dictionary of piece positions
    ///   - activeColor: Active color ("w" or "b")
    ///   - castlingRights: Castling rights string
    /// - Returns: true if chess logic (excluding en passant) is valid, false otherwise
    public static func isValidChessLogicExcludingEnPassant(pieces: [Character: [(Int, Int)]],
                                                          activeColor: String,
                                                          castlingRights: String) -> Bool {
        
        // Must have exactly one king of each color
        guard pieces["K"]?.count == 1 && pieces["k"]?.count == 1 else { return false }
        
        // Pawn count validation
        let whitePawnCount = pieces["P"]?.count ?? 0
        let blackPawnCount = pieces["p"]?.count ?? 0
        guard whitePawnCount <= 8 && blackPawnCount <= 8 else { return false }
        
        // Total piece count validation
        let whitePieceCount = pieces.filter { $0.key.isUppercase }.values.flatMap { $0 }.count
        let blackPieceCount = pieces.filter { $0.key.isLowercase }.values.flatMap { $0 }.count
        guard whitePieceCount <= 16 && blackPieceCount <= 16 else { return false }
        
        // Pawns cannot be on first or last rank
        if let whitePawns = pieces["P"] {
            for (_, rank) in whitePawns {
                guard rank != 0 && rank != 7 else { return false }
            }
        }
        if let blackPawns = pieces["p"] {
            for (_, rank) in blackPawns {
                guard rank != 0 && rank != 7 else { return false }
            }
        }
        
        guard isValidCastlingLogic(pieces: pieces, castlingRights: castlingRights) else { return false }
        guard isValidCheckLogic(pieces: pieces, activeColor: activeColor) else { return false }
        
        return true
    }
    
    /// Gets all meaningful en passant target squares for a given position
    /// Only returns squares where the active player has a pawn that can actually capture en passant
    /// - Parameters:
    ///   - pieces: Dictionary of piece positions
    ///   - activeColor: Active color ("w" or "b")
    /// - Returns: Array of meaningful en passant square strings (e.g., ["e3", "d6"])
    public static func getValidEnPassantSquares(pieces: [Character: [(Int, Int)]], activeColor: String) -> [String] {
        var meaningfulSquares: [String] = []
        
        if activeColor == "w" {
            // White to move - look for black pawns that just moved two squares (en passant target on rank 6)
            if let blackPawns = pieces["p"], let whitePawns = pieces["P"] {
                for (file, rank) in blackPawns {
                    if rank == 4 { // Black pawn on rank 5 (0-indexed rank 4)
                        let targetFile = Character(UnicodeScalar(file + Int("a".unicodeScalars.first!.value))!)
                        let targetSquare = "\(targetFile)6"
                        
                        // Check if target square is empty
                        let targetOccupied = pieces.values.flatMap { $0 }.contains { $0.0 == file && $0.1 == 5 }
                        if !targetOccupied {
                            // Check if white has a pawn that can capture en passant
                            let canCapture = whitePawns.contains { (pawnFile, pawnRank) in
                                pawnRank == 4 && // White pawn on rank 5 (0-indexed rank 4)
                                abs(pawnFile - file) == 1 // Adjacent file
                            }
                            
                            if canCapture {
                                meaningfulSquares.append(targetSquare)
                            }
                        }
                    }
                }
            }
        } else {
            // Black to move - look for white pawns that just moved two squares (en passant target on rank 3)
            if let whitePawns = pieces["P"], let blackPawns = pieces["p"] {
                for (file, rank) in whitePawns {
                    if rank == 3 { // White pawn on rank 4 (0-indexed rank 3)
                        let targetFile = Character(UnicodeScalar(file + Int("a".unicodeScalars.first!.value))!)
                        let targetSquare = "\(targetFile)3"
                        
                        // Check if target square is empty
                        let targetOccupied = pieces.values.flatMap { $0 }.contains { $0.0 == file && $0.1 == 2 }
                        if !targetOccupied {
                            // Check if black has a pawn that can capture en passant
                            let canCapture = blackPawns.contains { (pawnFile, pawnRank) in
                                pawnRank == 3 && // Black pawn on rank 4 (0-indexed rank 3)
                                abs(pawnFile - file) == 1 // Adjacent file
                            }
                            
                            if canCapture {
                                meaningfulSquares.append(targetSquare)
                            }
                        }
                    }
                }
            }
        }
        
        return meaningfulSquares.sorted()
    }
    
    /// Validates castling rights logic
    /// - Parameters:
    ///   - pieces: Dictionary of piece positions
    ///   - castlingRights: Castling rights string
    /// - Returns: true if castling logic is valid, false otherwise
    public static func isValidCastlingLogic(pieces: [Character: [(Int, Int)]], castlingRights: String) -> Bool {
        if castlingRights.contains("K") {
            guard pieces["K"]?.contains(where: { $0.0 == 4 && $0.1 == 0 }) == true else { return false }
            guard pieces["R"]?.contains(where: { $0.0 == 7 && $0.1 == 0 }) == true else { return false }
        }
        
        if castlingRights.contains("Q") {
            guard pieces["K"]?.contains(where: { $0.0 == 4 && $0.1 == 0 }) == true else { return false }
            guard pieces["R"]?.contains(where: { $0.0 == 0 && $0.1 == 0 }) == true else { return false }
        }
        
        if castlingRights.contains("k") {
            guard pieces["k"]?.contains(where: { $0.0 == 4 && $0.1 == 7 }) == true else { return false }
            guard pieces["r"]?.contains(where: { $0.0 == 7 && $0.1 == 7 }) == true else { return false }
        }
        
        if castlingRights.contains("q") {
            guard pieces["k"]?.contains(where: { $0.0 == 4 && $0.1 == 7 }) == true else { return false }
            guard pieces["r"]?.contains(where: { $0.0 == 0 && $0.1 == 7 }) == true else { return false }
        }
        
        return true
    }
    
    /// Validates en passant logic
    /// - Parameters:
    ///   - pieces: Dictionary of piece positions
    ///   - activeColor: Active color ("w" or "b")
    ///   - enPassant: En passant target square
    /// - Returns: true if en passant logic is valid, false otherwise
    public static func isValidEnPassantLogic(pieces: [Character: [(Int, Int)]], activeColor: String, enPassant: String) -> Bool {
        if enPassant == "-" { return true }
        
        guard enPassant.count == 2 else { return false }
        
        let chars = Array(enPassant)
        let file = Int(chars[0].asciiValue! - Character("a").asciiValue!)
        let rank = Int(String(chars[1]))!
        
        // En passant target square must be empty
        let targetSquareOccupied = pieces.values.flatMap { $0 }.contains { $0.0 == file && $0.1 == rank - 1 }
        guard !targetSquareOccupied else { return false }
        
        if rank == 3 {
            guard activeColor == "b" else { return false }
            guard pieces["P"]?.contains(where: { $0.0 == file && $0.1 == 3 }) == true else { return false }
        } else if rank == 6 {
            guard activeColor == "w" else { return false }
            guard pieces["p"]?.contains(where: { $0.0 == file && $0.1 == 4 }) == true else { return false }
        }
        
        return true
    }
    
    /// Validates that the opponent's king is not in check
    /// - Parameters:
    ///   - pieces: Dictionary of piece positions
    ///   - activeColor: Active color ("w" or "b")
    /// - Returns: true if check logic is valid, false otherwise
    public static func isValidCheckLogic(pieces: [Character: [(Int, Int)]], activeColor: String) -> Bool {
        
        let opponentColor: Character = activeColor == "w" ? "b" : "w"
        let opponentKing: Character = opponentColor == "w" ? "K" : "k"
        guard let kingPos = pieces[opponentKing]?.first else { return false }
        
        let attackingPieces = pieces.filter { key, _ in
            if activeColor == "w" {
                return key.isUppercase
            } else {
                return key.isLowercase
            }
        }
        
        for (pieceType, positions) in attackingPieces {
            for pos in positions {
                if canAttack(piece: pieceType, from: pos, to: kingPos, pieces: pieces) {
                    return false
                }
            }
        }
        
        return true
    }
    
    /// Checks if a piece can attack a target square
    /// - Parameters:
    ///   - piece: The attacking piece character
    ///   - from: Source position
    ///   - to: Target position
    ///   - pieces: Dictionary of all piece positions
    /// - Returns: true if the piece can attack the target, false otherwise
    public static func canAttack(piece: Character, from: (Int, Int), to: (Int, Int), pieces: [Character: [(Int, Int)]]) -> Bool {
        let dx = to.0 - from.0
        let dy = to.1 - from.1
        
        let allPieces = Set(pieces.values.flatMap { $0 }.map { "\($0.0),\($0.1)" })
        
        switch piece.lowercased().first! {
        case "p":
            let direction = piece.isUppercase ? 1 : -1
            return (dx == -1 || dx == 1) && dy == direction
        case "r":
            if dx == 0 || dy == 0 {
                return isPathClear(from: from, to: to, pieces: allPieces)
            }
        case "n":
            return (abs(dx) == 2 && abs(dy) == 1) || (abs(dx) == 1 && abs(dy) == 2)
        case "b":
            if abs(dx) == abs(dy) {
                return isPathClear(from: from, to: to, pieces: allPieces)
            }
        case "q":
            if dx == 0 || dy == 0 || abs(dx) == abs(dy) {
                return isPathClear(from: from, to: to, pieces: allPieces)
            }
        case "k":
            return abs(dx) <= 1 && abs(dy) <= 1
        default:
            return false
        }
        
        return false
    }
    
    /// Checks if the path between two squares is clear
    /// - Parameters:
    ///   - from: Source position
    ///   - to: Target position
    ///   - pieces: Set of occupied square coordinates as strings
    /// - Returns: true if the path is clear, false otherwise
    public static func isPathClear(from: (Int, Int), to: (Int, Int), pieces: Set<String>) -> Bool {
        let dx = to.0 - from.0
        let dy = to.1 - from.1
        
        let steps = max(abs(dx), abs(dy))
        let stepX = dx == 0 ? 0 : dx / abs(dx)
        let stepY = dy == 0 ? 0 : dy / abs(dy)
        
        for i in 1..<steps {
            let x = from.0 + i * stepX
            let y = from.1 + i * stepY
            
            if pieces.contains("\(x),\(y)") {
                return false
            }
        }
        
        return true
    }
}