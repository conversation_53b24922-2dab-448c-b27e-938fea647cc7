//
//  UndoState.swift
//  ChessKit
//
//  Created by Augment Agent on 2025/7/19.
//

import Foundation

// MARK: - Node Path System

/// Represents a path from the head node to any node in the MoveTree.
/// This path-based approach ensures position information remains valid across undo/redo operations.
public struct NodePath: Sendable, Equatable {
    /// Array of steps from head node to target node.
    /// -1 represents choosing the 'next' node, non-negative integers represent children array indices.
    public let steps: [Int]
    
    public init(steps: [Int]) {
        self.steps = steps
    }
    
    /// Creates a path from the head node to the specified node using the move sequence.
    /// This is safer than using direct node references.
    public static func pathTo(moveIndex targetIndex: MoveTree.MoveIndex, in moveTree: MoveTree) -> NodePath? {
        guard let targetNode = moveTree.dictionary[targetIndex] else { return nil }
        return pathToNode(targetNode, in: moveTree)
    }
    
    private static func pathToNode(_ targetNode: MoveTree.Node, in moveTree: MoveTree) -> NodePath? {
        var path: [Int] = []
        var current = targetNode
        
        // Traverse backwards from target to head
        while current.index != MoveTree.minimumIndex {
            guard let parent = current.previous else { return nil }
            
            // Determine the step from parent to current
            if parent.next?.index == current.index {
                // Current is the next node of parent
                path.insert(-1, at: 0)
            } else {
                // Current is in parent's children
                guard let childIndex = parent.children.firstIndex(where: { $0.index == current.index }) else {
                    return nil
                }
                path.insert(childIndex, at: 0)
            }
            
            current = parent
        }
        
        return NodePath(steps: path)
    }
    
    /// Finds the node at this path in the given MoveTree.
    internal func findNode(in moveTree: MoveTree) -> MoveTree.Node? {
        var current = moveTree.headNode
        
        for step in steps {
            if step == -1 {
                // Choose next node
                guard let next = current.next else { return nil }
                current = next
            } else {
                // Choose child at index
                guard step >= 0 && step < current.children.count else { return nil }
                current = current.children[step]
            }
        }
        
        return current
    }
    
    /// Returns the index of the node at this path, or nil if not found.
    public func findIndex(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        return findNode(in: moveTree)?.index
    }
}

// MARK: - Helper Data Structures

/// Lightweight value type for storing node information without reference cycles.
public struct SavedNode: Sendable {
    public let index: MoveTree.MoveIndex
    public let move: Move
    public let color: Piece.Color
    public let number: Int
    public let previousIndex: MoveTree.MoveIndex?
    public let nextIndex: MoveTree.MoveIndex?
    public let childrenIndices: [MoveTree.MoveIndex]
    
    // Backup path-based positioning for cross-boundary references
    // These are used when previousIndex might point to non-deleted nodes
    public let previousPath: NodePath?
    public let nextPath: NodePath?
    public let childrenPaths: [NodePath]?
    
    public init(index: MoveTree.MoveIndex, move: Move, color: Piece.Color, number: Int, 
                previousIndex: MoveTree.MoveIndex?, nextIndex: MoveTree.MoveIndex?, 
                childrenIndices: [MoveTree.MoveIndex],
                previousPath: NodePath? = nil, nextPath: NodePath? = nil, childrenPaths: [NodePath]? = nil) {
        self.index = index
        self.move = move
        self.color = color
        self.number = number
        self.previousIndex = previousIndex
        self.nextIndex = nextIndex
        self.childrenIndices = childrenIndices
        self.previousPath = previousPath
        self.nextPath = nextPath
        self.childrenPaths = childrenPaths
    }
    
    /// Create SavedNode from MoveTree.Node with optional path backup
    internal init(from node: MoveTree.Node, moveTree: MoveTree? = nil) {
        self.index = node.index
        self.move = node.move
        self.color = node.color
        self.number = node.number
        self.previousIndex = node.previous?.index
        self.nextIndex = node.next?.index
        self.childrenIndices = node.children.map { $0.index }
        
        // Generate backup paths for cross-boundary references
        if let moveTree = moveTree {
            self.previousPath = node.previous.flatMap { NodePath.pathTo(moveIndex: $0.index, in: moveTree) }
            self.nextPath = node.next.flatMap { NodePath.pathTo(moveIndex: $0.index, in: moveTree) }
            self.childrenPaths = node.children.compactMap { NodePath.pathTo(moveIndex: $0.index, in: moveTree) }
        } else {
            self.previousPath = nil
            self.nextPath = nil
            self.childrenPaths = nil
        }
    }
    
    /// Resolve the previous node index, falling back to path if index is invalid
    public func resolvePreviousIndex(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        // First try the stored index
        if let previousIndex = previousIndex, moveTree.dictionary[previousIndex] != nil {
            return previousIndex
        }
        
        // Fall back to path-based resolution
        return previousPath?.findIndex(in: moveTree)
    }
    
    /// Resolve the next node index, falling back to path if index is invalid
    public func resolveNextIndex(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        // First try the stored index
        if let nextIndex = nextIndex, moveTree.dictionary[nextIndex] != nil {
            return nextIndex
        }
        
        // Fall back to path-based resolution
        return nextPath?.findIndex(in: moveTree)
    }
    
    /// Resolve children indices, falling back to paths if indices are invalid
    public func resolveChildrenIndices(in moveTree: MoveTree) -> [MoveTree.MoveIndex] {
        // Try stored indices first
        let validIndices = childrenIndices.filter { moveTree.dictionary[$0] != nil }
        
        // If all indices are valid, return them
        if validIndices.count == childrenIndices.count {
            return childrenIndices
        }
        
        // Otherwise, try path-based resolution
        return childrenPaths?.compactMap { $0.findIndex(in: moveTree) } ?? []
    }
}

/// Records each promotion step for complex main variation promotions.
public struct PromotionStep: Sendable {
    public let variationRootIndex: MoveTree.MoveIndex
    public let parentIndex: MoveTree.MoveIndex  // parent of variationRoot
    public let originalNextIndex: MoveTree.MoveIndex
    public let originalSiblingOrder: [MoveTree.MoveIndex]
    
    // Backup path-based positioning for reliability
    public let variationRootPath: NodePath?
    public let parentPath: NodePath?
    public let originalNextPath: NodePath?
    
    public init(variationRootIndex: MoveTree.MoveIndex, parentIndex: MoveTree.MoveIndex,
                originalNextIndex: MoveTree.MoveIndex, originalSiblingOrder: [MoveTree.MoveIndex],
                variationRootPath: NodePath? = nil, parentPath: NodePath? = nil, originalNextPath: NodePath? = nil) {
        self.variationRootIndex = variationRootIndex
        self.parentIndex = parentIndex
        self.originalNextIndex = originalNextIndex
        self.originalSiblingOrder = originalSiblingOrder
        self.variationRootPath = variationRootPath
        self.parentPath = parentPath
        self.originalNextPath = originalNextPath
    }
}

// MARK: - Undo State Structures

/// Undo state for add move operations
public struct AddMoveUndoState: Sendable {
    public let parentIndex: MoveTree.MoveIndex
    public let move: Move
    public let position: Position
    
    // Backup path-based positioning for reliability when parentIndex becomes invalid
    public let parentPath: NodePath
    
    public init(parentIndex: MoveTree.MoveIndex, move: Move, position: Position, parentPath: NodePath) {
        self.parentIndex = parentIndex
        self.move = move
        self.position = position
        self.parentPath = parentPath
    }
    
    /// Gets the resolved parent index using robust resolution.
    /// - parameter moveTree: The move tree to resolve indices in
    /// - returns: The resolved parent index, or nil if not found
    public func resolvedParentIndex(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        if moveTree.dictionary[parentIndex] != nil {
            return parentIndex
        } else if let resolvedIndex = parentPath.findIndex(in: moveTree) {
            return resolvedIndex
        } else {
            return nil
        }
    }
    
    /// Gets the index of the move that should be removed during undo.
    /// - parameter moveTree: The move tree to find the move in
    /// - returns: The index of the move to remove, or nil if not found
    public func newMoveIndexToRemove(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        guard let parentIndex = resolvedParentIndex(in: moveTree),
              let parent = moveTree.dictionary[parentIndex] else {
            return nil
        }
        
        // Since add() prioritizes next then children, we reverse: check children first, then next
        if !parent.children.isEmpty {
            // The last child is the most recently added variation
            return parent.children.last?.index
        } else if let next = parent.next {
            // If no children, next was the first/only move added
            return next.index
        } else {
            return nil
        }
    }
}

/// Undo state for delete move operations
public struct DeleteMoveUndoState: Sendable {
    public let deletedNodes: [SavedNode]
    public let deletedPositions: [MoveTree.MoveIndex: Position]
    public let parentIndex: MoveTree.MoveIndex
    public let indexInParent: Int  // -1 = next, others = children position
    
    // Backup path-based positioning for reliability
    public let parentPath: NodePath?
    
    public init(deletedNodes: [SavedNode], deletedPositions: [MoveTree.MoveIndex: Position],
                parentIndex: MoveTree.MoveIndex, indexInParent: Int, parentPath: NodePath? = nil) {
        self.deletedNodes = deletedNodes
        self.deletedPositions = deletedPositions
        self.parentIndex = parentIndex
        self.indexInParent = indexInParent
        self.parentPath = parentPath
    }
}

/// Undo state for delete before move operations  
public struct DeleteBeforeMoveUndoState: Sendable {
    public let deletedNodes: [SavedNode]
    public let deletedPositions: [MoveTree.MoveIndex: Position]
    public let originalHeadNode: SavedNode
    public let originalStartingPosition: Position
    public let targetIndex: MoveTree.MoveIndex
    
    // Backup path-based positioning for reliability
    public let targetPath: NodePath?
    
    public init(deletedNodes: [SavedNode], deletedPositions: [MoveTree.MoveIndex: Position],
                originalHeadNode: SavedNode, originalStartingPosition: Position, 
                targetIndex: MoveTree.MoveIndex, targetPath: NodePath? = nil) {
        self.deletedNodes = deletedNodes
        self.deletedPositions = deletedPositions
        self.originalHeadNode = originalHeadNode
        self.originalStartingPosition = originalStartingPosition
        self.targetIndex = targetIndex
        self.targetPath = targetPath
    }
}

/// Undo state for promote variation operations
public struct PromoteVariationUndoState: Sendable {
    public let promoteStep: PromotionStep
    public let targetIndex: MoveTree.MoveIndex  // for restoring currentMoveIndex
    
    // Backup path-based positioning for reliability
    public let targetPath: NodePath?
    
    public init(promoteStep: PromotionStep, targetIndex: MoveTree.MoveIndex, targetPath: NodePath? = nil) {
        self.promoteStep = promoteStep
        self.targetIndex = targetIndex
        self.targetPath = targetPath
    }
}

/// Undo state for promote to main variation operations
public struct PromoteToMainUndoState: Sendable {
    public let promotionSteps: [PromotionStep]
    public let targetIndex: MoveTree.MoveIndex
    
    // Backup path-based positioning for reliability
    public let targetPath: NodePath?
    
    public init(promotionSteps: [PromotionStep], targetIndex: MoveTree.MoveIndex, targetPath: NodePath? = nil) {
        self.promotionSteps = promotionSteps
        self.targetIndex = targetIndex
        self.targetPath = targetPath
    }
}

/// Undo state for overwrite operations
public struct OverwriteUndoState: Sendable {
    public let deletedNodes: [SavedNode]
    public let deletedPositions: [MoveTree.MoveIndex: Position]
    public let parentIndex: MoveTree.MoveIndex
    
    // Backup path-based positioning for reliability when parentIndex becomes invalid
    public let parentPath: NodePath
    
    public init(deletedNodes: [SavedNode], deletedPositions: [MoveTree.MoveIndex: Position],
                parentIndex: MoveTree.MoveIndex, parentPath: NodePath) {
        self.deletedNodes = deletedNodes
        self.deletedPositions = deletedPositions
        self.parentIndex = parentIndex
        self.parentPath = parentPath
    }
    
    /// Gets the resolved parent index using robust resolution.
    /// - parameter moveTree: The move tree to resolve indices in
    /// - returns: The resolved parent index, or nil if not found
    public func resolvedParentIndex(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        if moveTree.dictionary[parentIndex] != nil {
            return parentIndex
        } else if let resolvedIndex = parentPath.findIndex(in: moveTree) {
            return resolvedIndex
        } else {
            return nil
        }
    }
    
    /// Gets the index of the new move that should be removed during undo.
    /// For overwrite operations, this is always the parent's next move.
    /// - parameter moveTree: The move tree to find the move in
    /// - returns: The index of the new move to remove, or nil if not found
    public func newMoveIndexToRemove(in moveTree: MoveTree) -> MoveTree.MoveIndex? {
        guard let parentIndex = resolvedParentIndex(in: moveTree),
              let parent = moveTree.dictionary[parentIndex] else {
            return nil
        }
        
        // For overwrite operations, the new move is always the parent's next
        return parent.next?.index
    }
}

/// Undo state for edit assessment/comment operations
public struct EditMoveUndoState: Sendable {
    public let moveIndex: MoveTree.MoveIndex
    public let originalMove: Move
    
    // Backup path-based positioning for reliability
    public let movePath: NodePath?
    
    public init(moveIndex: MoveTree.MoveIndex, originalMove: Move, movePath: NodePath? = nil) {
        self.moveIndex = moveIndex
        self.originalMove = originalMove
        self.movePath = movePath
    }
}

/// Undo state for import/position setup operations
public struct ImportGameUndoState: Sendable {
    public let originalGame: Game
    public let originalCurrentMoveIndex: MoveTree.MoveIndex
    public let originalBoard: Board
    
    public init(originalGame: Game, originalCurrentMoveIndex: MoveTree.MoveIndex, 
                originalBoard: Board) {
        self.originalGame = originalGame
        self.originalCurrentMoveIndex = originalCurrentMoveIndex
        self.originalBoard = originalBoard
    }
}
